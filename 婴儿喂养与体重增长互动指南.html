<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>婴儿喂养与体重增长互动指南</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Calm Harmony (Warm Neutrals with Muted Teal Accents) -->
    <!-- Application Structure Plan: A user-journey based SPA with 5 thematic sections (Growth Assessment, Breastfeeding, Formula, Solids, Common Issues). This structure guides parents logically from understanding growth to practical feeding advice and troubleshooting, making the dense report more accessible and task-oriented than a linear chapter-based layout. Key interactions like a growth curve visualizer, feeding calculator, and interactive food guide are central to this user-friendly approach. -->
    <!-- Visualization & Content Choices: 1. Report Info: WHO vs CDC growth patterns -> Goal: Compare -> Viz: Dual-line chart (Chart.js) -> Interaction: Toggle text -> Justification: Visually demonstrates the core difference faster than a table. 2. Report Info: Formula feeding amounts (Table 3) -> Goal: Inform -> Viz: Text output -> Interaction: Age slider -> Justification: Personalizes data, making it actionable. 3. Report Info: First foods nutrients (Table 5) -> Goal: Organize/Compare -> Viz: Interactive cards -> Interaction: Click-to-reveal modal -> Justification: More engaging and digestible than a static table, focuses on nutrient highlights. 4. Report Info: Under/Overfeeding signs (Table 6) -> Goal: Organize/Inform -> Viz: Accordion/Collapsible sections -> Interaction: Click to expand -> Justification: Organizes complex diagnostic info into a clean, non-overwhelming UI. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #FFFCF5; 
            color: #333;
        }
        .nav-link {
            transition: color 0.3s, border-bottom-color 0.3s;
            border-bottom: 2px solid transparent;
        }
        .nav-link:hover, .nav-link.active {
            color: #0d9488; /* teal-600 */
            border-bottom-color: #0d9488;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 40vh;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }
        .smooth-scroll {
            scroll-behavior: smooth;
        }
        .card-enter {
            animation: card-enter 0.5s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }
        @keyframes card-enter {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="smooth-scroll">

    <!-- Header and Navigation -->
    <header class="bg-[#FFF8ED]/80 backdrop-blur-sm sticky top-0 z-50 shadow-sm">
        <div class="container mx-auto max-w-6xl px-4">
            <nav class="flex justify-between items-center py-4">
                <h1 class="text-xl md:text-2xl font-bold text-teal-800">喂养与成长指南</h1>
                <div class="hidden md:flex space-x-6">
                    <a href="#growth-assessment" class="nav-link font-medium text-slate-700 pb-1">生长评估</a>
                    <a href="#breastfeeding" class="nav-link font-medium text-slate-700 pb-1">母乳喂养</a>
                    <a href="#formula-feeding" class="nav-link font-medium text-slate-700 pb-1">配方奶</a>
                    <a href="#solid-foods" class="nav-link font-medium text-slate-700 pb-1">辅食添加</a>
                    <a href="#common-issues" class="nav-link font-medium text-slate-700 pb-1">常见问题</a>
                </div>
                <button id="mobile-menu-button" class="md:hidden text-slate-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>
                </button>
            </nav>
            <div id="mobile-menu" class="hidden md:hidden py-2">
                <a href="#growth-assessment" class="block py-2 px-2 text-slate-700 hover:bg-teal-50 rounded">生长评估</a>
                <a href="#breastfeeding" class="block py-2 px-2 text-slate-700 hover:bg-teal-50 rounded">母乳喂养</a>
                <a href="#formula-feeding" class="block py-2 px-2 text-slate-700 hover:bg-teal-50 rounded">配方奶</a>
                <a href="#solid-foods" class="block py-2 px-2 text-slate-700 hover:bg-teal-50 rounded">辅食添加</a>
                <a href="#common-issues" class="block py-2 px-2 text-slate-700 hover:bg-teal-50 rounded">常见问题</a>
            </div>
        </div>
    </header>

    <main class="container mx-auto max-w-5xl px-4 py-8 md:py-12">

        <!-- Section 1: Growth Assessment -->
        <section id="growth-assessment" class="mb-16">
            <h2 class="text-3xl font-bold text-center text-teal-800 mb-2">第一步：理解宝宝的生长曲线</h2>
            <p class="text-center text-slate-600 mb-8 max-w-3xl mx-auto">生长曲线是评估宝宝健康的关键工具，但不同的标准讲述着不同的故事。了解WHO和CDC标准的区别，是科学解读宝宝成长的第一步。</p>
            
            <div class="bg-white rounded-xl shadow-lg p-6 md:p-8">
                <div class="chart-container mb-6">
                    <canvas id="growthChart"></canvas>
                </div>
                <div class="text-center mb-6">
                    <p class="text-slate-600">上图展示了典型的母乳喂养婴儿（WHO标准）与主要以配方奶喂养的婴儿（CDC历史数据）的体重增长模式差异。母乳宝宝初期增长快，之后会自然放缓。</p>
                </div>
                <div class="grid md:grid-cols-2 gap-6">
                    <div id="who-card" class="border-2 border-teal-500 rounded-lg p-4 bg-teal-50 transition-all duration-300">
                        <h3 class="font-bold text-xl text-teal-700 mb-2">WHO生长标准 (黄金标准)</h3>
                        <p class="text-slate-700 text-sm">这是一个“规范性”标准，描述了在理想条件下（以母乳喂养为主）宝宝*应该*如何生长。它将母乳喂养的独特生长模式（初期快，后放缓）正常化，是当前0-2岁婴幼儿评估的推荐标准，有助于避免对健康的母乳宝宝做出不必要的干预。</p>
                    </div>
                    <div id="cdc-card" class="border-2 border-slate-300 rounded-lg p-4 bg-white transition-all duration-300">
                        <h3 class="font-bold text-xl text-slate-700 mb-2">CDC生长图表 (历史参考)</h3>
                        <p class="text-slate-700 text-sm">这是一个“描述性”参考，反映了过去美国婴儿（以配方奶喂养为主）的*实际*生长情况。其体重增长模式更线性、更快速。若用此标准衡量母乳宝宝，可能在3个月后误判其为“生长迟缓”。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: Breastfeeding -->
        <section id="breastfeeding" class="mb-16">
            <h2 class="text-3xl font-bold text-center text-teal-800 mb-2">母乳喂养：黄金标准的实践</h2>
            <p class="text-center text-slate-600 mb-8 max-w-3xl mx-auto">母乳是宝宝最理想的食物。由于无法直接测量奶量，我们可以通过观察一系列可靠的迹象来判断宝宝是否获得了充足的营养。</p>
            <div class="bg-white rounded-xl shadow-lg p-6 md:p-8">
                <h3 class="text-2xl font-bold text-center text-teal-700 mb-6">我的母乳宝宝吃饱了吗？- 营养充足性自查清单</h3>
                <div class="grid md:grid-cols-3 gap-6 text-center">
                    <div class="bg-amber-50 rounded-lg p-4 card-enter">
                        <div class="text-4xl mb-2">🍼</div>
                        <h4 class="font-bold text-lg text-amber-800 mb-1">喂养行为</h4>
                        <p class="text-sm text-slate-600">24小时内喂养8-12次。喂奶时能听到吞咽声。喂完后宝宝看起来满足、放松。</p>
                    </div>
                    <div class="bg-amber-50 rounded-lg p-4 card-enter" style="animation-delay: 0.1s;">
                        <div class="text-4xl mb-2">⚖️</div>
                        <h4 class="font-bold text-lg text-amber-800 mb-1">体重增长</h4>
                        <p class="text-sm text-slate-600">出生10-14天内恢复出生体重。之后体重沿自己的生长曲线稳定增长。</p>
                    </div>
                    <div class="bg-amber-50 rounded-lg p-4 card-enter" style="animation-delay: 0.2s;">
                        <div class="text-4xl mb-2">🚼</div>
                        <h4 class="font-bold text-lg text-amber-800 mb-1">尿布输出</h4>
                        <p class="text-sm text-slate-600">出生第5天后，每天至少6片湿尿布（尿液清澈或淡黄），和3-4次黄色糊状大便。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: Formula Feeding -->
        <section id="formula-feeding" class="mb-16">
            <h2 class="text-3xl font-bold text-center text-teal-800 mb-2">配方奶：安全健康的替代选择</h2>
            <p class="text-center text-slate-600 mb-8 max-w-3xl mx-auto">对于配方奶喂养的宝宝，了解大致的喂养量和响应式喂养技巧，有助于支持宝宝健康成长，同时避免过度喂养。</p>
            <div class="bg-white rounded-xl shadow-lg p-6 md:p-8">
                 <h3 class="text-2xl font-bold text-center text-teal-700 mb-2">配方奶喂养量参考</h3>
                 <p class="text-center text-slate-500 text-sm mb-6">拖动滑块查看不同月龄宝宝的大致每日总奶量。请记住，这只是参考，关键是响应宝宝的饥饿和饱腹信号。</p>
                <div class="max-w-md mx-auto">
                    <label for="age-slider" class="block text-center font-medium text-slate-700 mb-2">宝宝月龄: <span id="age-display" class="font-bold text-teal-600">1</span> 个月</label>
                    <input id="age-slider" type="range" min="0" max="12" value="1" step="1" class="w-full h-2 bg-teal-100 rounded-lg appearance-none cursor-pointer">
                    <div class="mt-6 bg-teal-50 rounded-lg p-4 text-center">
                        <p class="text-slate-700">每日建议总奶量约:</p>
                        <p id="formula-amount" class="text-3xl font-bold text-teal-600">540 - 960 mL</p>
                        <p class="text-xs text-slate-500 mt-2">通用估算法则：约150 mL/kg/天。每日最高一般不超过960 mL。</p>
                    </div>
                </div>
                 <div class="mt-8 border-t pt-6">
                    <h4 class="text-xl font-bold text-center text-teal-700 mb-3">预防过度喂养小贴士</h4>
                    <p class="text-center text-sm text-slate-600">瓶喂时，采用“慢速瓶喂法”：让宝宝近乎直立，奶瓶保持水平，让宝宝主导吸吮节奏，喂几分钟后可暂停拍嗝，给宝宝感知饱腹感的机会。切勿强迫宝宝喝完最后一滴奶。</p>
                </div>
            </div>
        </section>

        <!-- Section 4: Solid Foods -->
        <section id="solid-foods" class="mb-16">
            <h2 class="text-3xl font-bold text-center text-teal-800 mb-2">辅食添加：开启味觉新旅程</h2>
            <p class="text-center text-slate-600 mb-8 max-w-3xl mx-auto">约6个月大，当宝宝准备好时，就可以引入辅食了。选择营养密集的食物，并以响应式、安全的方式喂养是关键。</p>
            
            <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 mb-8">
                <h3 class="text-2xl font-bold text-center text-teal-700 mb-6">宝宝准备好了吗？- 发育准备度信号</h3>
                <ul class="grid md:grid-cols-2 lg:grid-cols-4 gap-4 text-center">
                    <li class="bg-amber-50 rounded-lg p-3"><span class="font-bold">✓</span> 能在支撑下坐直</li>
                    <li class="bg-amber-50 rounded-lg p-3"><span class="font-bold">✓</span> 头部控制良好</li>
                    <li class="bg-amber-50 rounded-lg p-3"><span class="font-bold">✓</span> 挺舌反射消失</li>
                    <li class="bg-amber-50 rounded-lg p-3"><span class="font-bold">✓</span> 对食物表现出兴趣</li>
                </ul>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 md:p-8">
                <h3 class="text-2xl font-bold text-center text-teal-700 mb-6">首批辅食的营养选择（点击查看详情）</h3>
                <div id="food-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    <!-- Food cards will be injected by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Section 5: Common Issues -->
        <section id="common-issues" class="mb-16">
            <h2 class="text-3xl font-bold text-center text-teal-800 mb-2">常见喂养问题解答</h2>
            <p class="text-center text-slate-600 mb-8 max-w-3xl mx-auto">在喂养过程中遇到困惑是正常的。这里我们解答一些常见问题，帮助您从容应对。</p>
            <div id="accordion" class="space-y-4">
                <!-- Accordion items will be injected by JavaScript -->
            </div>
        </section>

    </main>
    
    <!-- Footer -->
    <footer class="bg-slate-100 border-t">
        <div class="container mx-auto max-w-6xl px-4 py-6 text-center text-slate-500 text-sm">
            <p>本应用内容基于《婴儿体重与母乳、配方奶及辅食喂养实践的综合分析》报告制作，仅供参考，不能替代专业的医疗建议。</p>
            <p>如有任何健康问题，请咨询您的儿科医生。</p>
        </div>
    </footer>

    <!-- Modal for Food Details -->
    <div id="food-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-sm w-full p-6 relative">
            <button id="close-modal-button" class="absolute top-2 right-3 text-2xl text-slate-500 hover:text-slate-800">&times;</button>
            <h3 id="modal-title" class="text-2xl font-bold text-teal-700 mb-3"></h3>
            <p id="modal-description" class="text-slate-600 mb-4"></p>
            <div class="border-t pt-4">
                <p class="font-semibold text-slate-800">关键营养亮点:</p>
                <p id="modal-highlights" class="text-teal-800"></p>
            </div>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // --- Data Store ---
    const formulaData = [
        { age: 0, range: "240 - 720 mL" },
        { age: 1, range: "540 - 960 mL" },
        { age: 2, range: "600 - 1170 mL" },
        { age: 3, range: "600 - 1170 mL" },
        { age: 4, range: "600 - 1080 mL" },
        { age: 5, range: "600 - 1080 mL" },
        { age: 6, range: "720 - 960 mL" },
        { age: 7, range: "720 - 960 mL" },
        { age: 8, range: "720 - 960 mL" },
        { age: 9, range: "840 - 960+ mL" },
        { age: 10, range: "840 - 960+ mL" },
        { age: 11, range: "840 - 960+ mL" },
        { age: 12, range: "630 - 960 mL" }
    ];

    const firstFoods = [
        { name: '鸡肉泥', icon: '🍗', description: '优质蛋白质和高生物利用率的血红素铁的极佳来源，支持肌肉和血液健康。', highlights: '高吸收率铁、优质蛋白' },
        { name: '牛油果', icon: '🥑', description: '富含健康的单不饱和脂肪，对宝宝的大脑和神经系统发育至关重要。', highlights: '健康脂肪、促进大脑发育' },
        { name: '红薯', icon: '🍠', description: 'β-胡萝卜素（维生素A）的极佳来源，支持视力和免疫系统，同时富含维生素C。', highlights: '维生素A、维生素C' },
        { name: '强化铁米粉', icon: '🌾', description: '作为铁的强化载体，是预防缺铁性贫血的传统首选辅食之一。', highlights: '强化铁、易于消化' },
        { name: '香蕉', icon: '🍌', description: '提供钾和维生素B6，质地柔软，味道香甜，是宝宝易于接受的水果。', highlights: '钾、维生素B6、易于接受' }
    ];
    
    const accordionData = [
        {
            title: "宝宝体重增长慢怎么办？",
            content: "首先，请咨询儿科医生进行专业评估。常见原因包括：无效的乳汁转移（衔乳姿势不当）、喂养频率不足（未按需喂养，每天应达8-12次）、或母亲乳汁供应问题。管理策略包括：改善喂养姿势、增加喂养频率。在医生指导下，有时可能需要补充喂养。"
        },
        {
            title: "如何判断是否过度喂养？",
            content: "过度喂养在瓶喂宝宝中更常见。迹象包括：喂食后烦躁不安、胀气、频繁大量的吐奶（非普通溢奶）、体重增长过快。预防的关键是“响应式喂养”：学习并尊重宝宝的饱腹信号（如转头、闭嘴）。采用“慢速瓶喂法”能有效帮助宝宝控制进食节奏。"
        },
        {
            title: "什么时候可以引入牛奶？(根据2023 WHO新指南)",
            content: "对于6-11个月的非母乳喂养宝宝，在保证辅食营养全面、富含铁质的前提下，可以将全脂牛奶作为配方奶的替代选项之一。关键在于，牛奶不能取代营养丰富的辅食，尤其是富含铁的食物。对于1岁以上幼儿，WHO推荐直接饮用动物奶，而非“成长配方奶”。"
        },
        {
            title: "如何引入花生、鸡蛋等易过敏食物？",
            content: "与旧观念相反，新研究表明在宝宝约6个月大时尽早引入常见致敏食物，可能有助于降低过敏风险。原则是：一次只引入一种新食物，少量开始，连续观察3-5天，看有无皮疹、呕吐、腹泻等不良反应。如果没有，可逐渐增加并纳入日常饮食。对于有严重湿疹或已知食物过敏史的宝宝，请在医生指导下进行。"
        }
    ];

    // --- Mobile Menu ---
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
    });

    // --- Smooth Scrolling for Nav Links ---
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
            if (mobileMenu.classList.contains('hidden') === false) {
                mobileMenu.classList.add('hidden');
            }
        });
    });
    
    // --- Section Observer for Active Nav Link ---
    const sections = document.querySelectorAll('section');
    const navLinks = document.querySelectorAll('.nav-link');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                navLinks.forEach(link => {
                    link.classList.toggle('active', link.getAttribute('href').substring(1) === entry.target.id);
                });
            }
        });
    }, { rootMargin: "-50% 0px -50% 0px" });

    sections.forEach(section => observer.observe(section));


    // --- Growth Chart (Chart.js) ---
    const ctx = document.getElementById('growthChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['出生', '1月', '2月', '3月', '4月', '6月', '9月', '12月'],
            datasets: [
                {
                    label: '母乳喂养 (WHO 标准模式)',
                    data: [3.3, 4.4, 5.6, 6.4, 7.0, 7.9, 8.9, 9.6],
                    borderColor: '#0d9488', // teal-600
                    backgroundColor: 'rgba(13, 148, 136, 0.1)',
                    borderWidth: 3,
                    tension: 0.3,
                    fill: true,
                },
                {
                    label: '配方奶喂养 (CDC 参考模式)',
                    data: [3.3, 4.5, 5.8, 6.8, 7.6, 8.8, 10.0, 10.8],
                    borderColor: '#9ca3af', // gray-400
                    backgroundColor: 'rgba(156, 163, 175, 0.1)',
                    borderWidth: 3,
                    tension: 0.3,
                    borderDash: [5, 5],
                    fill: true,
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '婴幼儿体重增长模式对比 (示意图)',
                    font: { size: 16, family: "'Noto Sans SC', sans-serif" },
                    color: '#333'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y + ' kg';
                            }
                            return label;
                        }
                    }
                },
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: '体重 (kg)'
                    }
                },
                x: {
                   title: {
                        display: true,
                        text: '月龄'
                    }
                }
            }
        }
    });

    // --- Formula Feeding Calculator ---
    const ageSlider = document.getElementById('age-slider');
    const ageDisplay = document.getElementById('age-display');
    const formulaAmount = document.getElementById('formula-amount');

    ageSlider.addEventListener('input', (e) => {
        const age = parseInt(e.target.value);
        ageDisplay.textContent = age === 0 ? "新生儿" : `${age} 个月`;
        const amountData = formulaData.find(d => d.age === age);
        formulaAmount.textContent = amountData ? amountData.range : "请咨询医生";
    });
    ageSlider.dispatchEvent(new Event('input')); // Initial call

    // --- First Foods Grid and Modal ---
    const foodGrid = document.getElementById('food-grid');
    const foodModal = document.getElementById('food-modal');
    const closeModalButton = document.getElementById('close-modal-button');
    const modalTitle = document.getElementById('modal-title');
    const modalDescription = document.getElementById('modal-description');
    const modalHighlights = document.getElementById('modal-highlights');

    firstFoods.forEach(food => {
        const card = document.createElement('div');
        card.className = 'bg-white border border-slate-200 rounded-lg p-4 text-center cursor-pointer hover:shadow-md hover:border-teal-300 transition-all card-enter';
        card.innerHTML = `
            <div class="text-5xl mb-3">${food.icon}</div>
            <h4 class="font-bold text-slate-800">${food.name}</h4>
        `;
        card.addEventListener('click', () => {
            modalTitle.textContent = `${food.icon} ${food.name}`;
            modalDescription.textContent = food.description;
            modalHighlights.textContent = food.highlights;
            foodModal.classList.remove('hidden');
        });
        foodGrid.appendChild(card);
    });

    closeModalButton.addEventListener('click', () => {
        foodModal.classList.add('hidden');
    });
    foodModal.addEventListener('click', (e) => {
        if(e.target === foodModal) {
            foodModal.classList.add('hidden');
        }
    });

    // --- Accordion for Common Issues ---
    const accordion = document.getElementById('accordion');
    accordionData.forEach((item, index) => {
        const accordionItem = document.createElement('div');
        accordionItem.className = 'bg-white rounded-xl shadow-sm overflow-hidden';
        accordionItem.innerHTML = `
            <button class="accordion-button w-full flex justify-between items-center text-left p-4 md:p-5 font-medium text-slate-800 hover:bg-teal-50 focus:outline-none">
                <span>${item.title}</span>
                <svg class="accordion-icon w-5 h-5 shrink-0 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 2 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
            </button>
            <div class="accordion-content hidden p-4 md:p-5 border-t border-slate-200">
                <p class="text-slate-600">${item.content}</p>
            </div>
        `;
        accordion.appendChild(accordionItem);
    });

    document.querySelectorAll('.accordion-button').forEach(button => {
        button.addEventListener('click', () => {
            const content = button.nextElementSibling;
            const icon = button.querySelector('.accordion-icon');
            
            content.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        });
    });

});
</script>

</body>
</html>
