// WHO儿童生长标准数据 (0-60个月)
// 数据来源: WHO Child Growth Standards
// 包含3rd, 15th, 50th, 85th, 97th百分位数据

const GROWTH_STANDARDS = {
    // 男孩身高标准 (cm)
    height_male: {
        0: { p3: 46.1, p15: 48.0, p50: 49.9, p85: 51.8, p97: 53.7 },
        1: { p3: 50.8, p15: 52.8, p50: 54.7, p85: 56.7, p97: 58.6 },
        2: { p3: 54.4, p15: 56.4, p50: 58.4, p85: 60.4, p97: 62.4 },
        3: { p3: 57.3, p15: 59.4, p50: 61.4, p85: 63.5, p97: 65.5 },
        4: { p3: 59.7, p15: 61.8, p50: 63.9, p85: 66.0, p97: 68.0 },
        5: { p3: 61.7, p15: 63.8, p50: 65.9, p85: 68.0, p97: 70.1 },
        6: { p3: 63.3, p15: 65.5, p50: 67.6, p85: 69.8, p97: 71.9 },
        9: { p3: 67.7, p15: 70.1, p50: 72.0, p85: 74.2, p97: 76.5 },
        12: { p3: 71.0, p15: 73.4, p50: 75.7, p85: 78.1, p97: 80.5 },
        15: { p3: 74.1, p15: 76.6, p50: 79.1, p85: 81.7, p97: 84.2 },
        18: { p3: 76.9, p15: 79.6, p50: 82.3, p85: 85.1, p97: 87.8 },
        24: { p3: 81.7, p15: 84.8, p50: 87.8, p85: 90.9, p97: 93.9 },
        30: { p3: 86.0, p15: 89.3, p50: 92.7, p85: 96.1, p97: 99.4 },
        36: { p3: 90.0, p15: 93.6, p50: 97.2, p85: 100.8, p97: 104.5 },
        42: { p3: 93.8, p15: 97.6, p50: 101.4, p85: 105.3, p97: 109.1 },
        48: { p3: 97.5, p15: 101.4, p50: 105.4, p85: 109.4, p97: 113.5 },
        54: { p3: 101.1, p15: 105.2, p50: 109.4, p85: 113.6, p97: 117.7 },
        60: { p3: 104.6, p15: 108.9, p50: 113.2, p85: 117.6, p97: 121.9 }
    },

    // 女孩身高标准 (cm)
    height_female: {
        0: { p3: 45.4, p15: 47.3, p50: 49.1, p85: 51.0, p97: 52.9 },
        1: { p3: 50.0, p15: 52.0, p50: 53.7, p85: 55.6, p97: 57.6 },
        2: { p3: 53.0, p15: 55.0, p50: 57.1, p85: 59.1, p97: 61.1 },
        3: { p3: 55.6, p15: 57.7, p50: 59.8, p85: 61.9, p97: 64.0 },
        4: { p3: 57.8, p15: 59.9, p50: 62.1, p85: 64.3, p97: 66.4 },
        5: { p3: 59.6, p15: 61.8, p50: 64.0, p85: 66.2, p97: 68.5 },
        6: { p3: 61.2, p15: 63.5, p50: 65.7, p85: 68.0, p97: 70.3 },
        9: { p3: 65.3, p15: 67.7, p50: 70.1, p85: 72.6, p97: 75.0 },
        12: { p3: 68.9, p15: 71.4, p50: 74.0, p85: 76.6, p97: 79.2 },
        15: { p3: 72.2, p15: 74.9, p50: 77.5, p85: 80.2, p97: 82.9 },
        18: { p3: 75.3, p15: 78.0, p50: 80.7, p85: 83.5, p97: 86.2 },
        24: { p3: 80.8, p15: 83.7, p50: 86.6, p85: 89.6, p97: 92.5 },
        30: { p3: 85.7, p15: 88.9, p50: 92.1, p85: 95.3, p97: 98.4 },
        36: { p3: 90.3, p15: 93.6, p50: 96.9, p85: 100.3, p97: 103.6 },
        42: { p3: 94.6, p15: 98.1, p50: 101.6, p85: 105.0, p97: 108.5 },
        48: { p3: 98.9, p15: 102.5, p50: 106.1, p85: 109.7, p97: 113.3 },
        54: { p3: 103.0, p15: 106.7, p50: 110.5, p85: 114.2, p97: 117.9 },
        60: { p3: 107.0, p15: 110.9, p50: 114.7, p85: 118.5, p97: 122.4 }
    },

    // 男孩体重标准 (kg)
    weight_male: {
        0: { p3: 2.5, p15: 2.9, p50: 3.3, p85: 3.9, p97: 4.4 },
        1: { p3: 3.4, p15: 3.9, p50: 4.5, p85: 5.1, p97: 5.8 },
        2: { p3: 4.3, p15: 4.9, p50: 5.6, p85: 6.3, p97: 7.1 },
        3: { p3: 5.0, p15: 5.7, p50: 6.4, p85: 7.2, p97: 8.0 },
        4: { p3: 5.6, p15: 6.2, p50: 7.0, p85: 7.8, p97: 8.7 },
        5: { p3: 6.0, p15: 6.7, p50: 7.5, p85: 8.4, p97: 9.3 },
        6: { p3: 6.4, p15: 7.1, p50: 7.9, p85: 8.8, p97: 9.8 },
        9: { p3: 7.1, p15: 7.8, p50: 8.9, p85: 9.9, p97: 11.0 },
        12: { p3: 7.7, p15: 8.4, p50: 9.6, p85: 10.8, p97: 12.0 },
        15: { p3: 8.3, p15: 9.0, p50: 10.3, p85: 11.7, p97: 13.0 },
        18: { p3: 8.8, p15: 9.6, p50: 10.9, p85: 12.4, p97: 14.0 },
        24: { p3: 9.7, p15: 10.5, p50: 12.2, p85: 13.9, p97: 15.8 },
        30: { p3: 10.5, p15: 11.4, p50: 13.3, p85: 15.3, p97: 17.5 },
        36: { p3: 11.3, p15: 12.2, p50: 14.3, p85: 16.7, p97: 19.1 },
        42: { p3: 12.0, p15: 13.0, p50: 15.3, p85: 18.0, p97: 20.7 },
        48: { p3: 12.7, p15: 13.7, p50: 16.2, p85: 19.4, p97: 22.3 },
        54: { p3: 13.4, p15: 14.5, p50: 17.1, p85: 20.7, p97: 23.9 },
        60: { p3: 14.1, p15: 15.2, p50: 18.0, p85: 22.0, p97: 25.5 }
    },

    // 女孩体重标准 (kg)
    weight_female: {
        0: { p3: 2.4, p15: 2.8, p50: 3.2, p85: 3.7, p97: 4.2 },
        1: { p3: 3.2, p15: 3.6, p50: 4.2, p85: 4.8, p97: 5.5 },
        2: { p3: 3.9, p15: 4.5, p50: 5.1, p85: 5.8, p97: 6.6 },
        3: { p3: 4.5, p15: 5.2, p50: 5.8, p85: 6.6, p97: 7.5 },
        4: { p3: 5.0, p15: 5.7, p50: 6.4, p85: 7.3, p97: 8.2 },
        5: { p3: 5.4, p15: 6.1, p50: 6.9, p85: 7.8, p97: 8.8 },
        6: { p3: 5.7, p15: 6.5, p50: 7.3, p85: 8.2, p97: 9.3 },
        9: { p3: 6.6, p15: 7.3, p50: 8.2, p85: 9.3, p97: 10.5 },
        12: { p3: 7.0, p15: 7.8, p50: 8.9, p85: 10.1, p97: 11.5 },
        15: { p3: 7.4, p15: 8.2, p50: 9.5, p85: 10.9, p97: 12.4 },
        18: { p3: 7.7, p15: 8.6, p50: 10.1, p85: 11.6, p97: 13.2 },
        24: { p3: 8.4, p15: 9.4, p50: 11.0, p85: 12.8, p97: 14.8 },
        30: { p3: 9.0, p15: 10.1, p50: 11.9, p85: 14.0, p97: 16.4 },
        36: { p3: 9.6, p15: 10.8, p50: 12.7, p85: 15.1, p97: 17.9 },
        42: { p3: 10.2, p15: 11.4, p50: 13.5, p85: 16.2, p97: 19.4 },
        48: { p3: 10.8, p15: 12.0, p50: 14.3, p85: 17.2, p97: 20.9 },
        54: { p3: 11.3, p15: 12.6, p50: 15.0, p85: 18.2, p97: 22.3 },
        60: { p3: 11.9, p15: 13.3, p50: 15.8, p85: 19.2, p97: 23.7 }
    }
};

// 获取最接近年龄的标准数据
function getClosestStandard(ageInMonths, gender, type) {
    const key = `${type}_${gender}`;
    const standards = GROWTH_STANDARDS[key];
    
    if (!standards) return null;
    
    // 找到最接近的年龄
    const ages = Object.keys(standards).map(Number).sort((a, b) => a - b);
    let closestAge = ages[0];
    
    for (let age of ages) {
        if (Math.abs(age - ageInMonths) < Math.abs(closestAge - ageInMonths)) {
            closestAge = age;
        }
    }
    
    return standards[closestAge];
}

// 计算百分位数
function calculatePercentile(value, ageInMonths, gender, type) {
    const standard = getClosestStandard(ageInMonths, gender, type);
    if (!standard) return null;
    
    if (value <= standard.p3) return 3;
    if (value <= standard.p15) return 15;
    if (value <= standard.p50) return 50;
    if (value <= standard.p85) return 85;
    if (value <= standard.p97) return 97;
    return 97;
}

// 获取生长评估
function getGrowthAssessment(percentile, type) {
    const typeText = type === 'height' ? '身高' : '体重';
    
    if (percentile < 3) {
        return {
            status: 'low',
            message: `${typeText}偏低，建议咨询儿科医生`,
            color: '#e74c3c'
        };
    } else if (percentile < 15) {
        return {
            status: 'below_average',
            message: `${typeText}略低于平均水平，继续观察`,
            color: '#f39c12'
        };
    } else if (percentile <= 85) {
        return {
            status: 'normal',
            message: `${typeText}在正常范围内`,
            color: '#27ae60'
        };
    } else if (percentile <= 97) {
        return {
            status: 'above_average',
            message: `${typeText}略高于平均水平`,
            color: '#f39c12'
        };
    } else {
        return {
            status: 'high',
            message: `${typeText}偏高，建议咨询儿科医生`,
            color: '#e74c3c'
        };
    }
}
