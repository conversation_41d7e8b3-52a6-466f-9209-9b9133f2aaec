

import os
import glob
import traceback
from docx import Document
from transformers import pipeline, AutoTokenizer, AutoModelForSeq2SeqLM

# --- CONFIGURATION ---
CONFIG = {
    "model_name": "google/flan-t5-small",
    "min_summary_length": 30,
    "max_summary_length": 150,
    "output_filename_suffix": "_professional.html"
}

# --- NLP MODEL INITIALIZATION ---
# A robust, one-time initialization of the summarization pipeline.
try:
    print("Initializing NLP model. This may take a moment on the first run...")
    summarizer = pipeline(
        "summarization",
        model=CONFIG["model_name"],
        tokenizer=CONFIG["model_name"]
    )
    print("NLP model ready.")
except Exception as e:
    print(f"FATAL: Could not load NLP model '{CONFIG['model_name']}'.")
    print(f"Error: {e}\nPlease check your internet connection and library installations.")
    summarizer = None

def get_heading_level(paragraph):
    """Gets the heading level from the paragraph's style name. Returns 0 if not a heading."""
    style_name = paragraph.style.name
    if style_name.startswith('Heading') or style_name.startswith('标题'):
        try:
            # Extracts the number from 'Heading 1', '标题2', etc.
            return int(''.join(filter(str.isdigit, style_name)))
        except ValueError:
            return 1 # Default to level 1 for non-numbered headings
    return 0

def parse_docx_structure(doc):
    """Parses the docx into a hierarchical structure of slides."""
    structured_slides = []
    current_slide = None

    for para in doc.paragraphs:
        if not para.text.strip():
            continue

        level = get_heading_level(para)
        if level > 0:
            # Finish the previous slide
            if current_slide:
                structured_slides.append(current_slide)
            
            # Start a new slide
            current_slide = {
                "title": para.text.strip(),
                "level": level,
                "content": []
            }
        elif current_slide:
            current_slide["content"].append(para.text.strip())
    
    # Append the last slide
    if current_slide:
        structured_slides.append(current_slide)
        
    return structured_slides

def summarize_content(text_content):
    """Summarizes a block of text if the summarizer is available."""
    if not summarizer or not text_content:
        return ""
    
    # The "summarize: " prefix is a good practice for T5 models.
    input_text = "summarize: " + text_content
    try:
        summary_result = summarizer(
            input_text,
            min_length=CONFIG["min_summary_length"],
            max_length=CONFIG["max_summary_length"],
            truncation=True
        )
        return summary_result[0]['summary_text']
    except Exception as e:
        print(f"  - Warning: Summarization failed. {e}")
        return "[Summarization failed for this section]"


def generate_html(title, slides_data):
    """Generates the final Reveal.js HTML with nested slides."""
    
    def create_slides_html(slides):
        html = ""
        i = 0
        while i < len(slides):
            slide = slides[i]
            # Summarize the collected content for the slide
            content_to_summarize = " ".join(slide['content'])
            summary = summarize_content(content_to_summarize)
            
            # Find child slides
            child_slides = []
            j = i + 1
            while j < len(slides) and slides[j]['level'] > slide['level']:
                child_slides.append(slides[j])
                j += 1
            
            if not child_slides:
                # Single slide without children
                html += f"<section><h2>{slide['title']}</h2>"
                if summary:
                    html += f"<blockquote>{summary}</blockquote>"
                html += "</section>\n"
                i += 1
            else:
                # A slide with vertical sub-slides
                html += "<section>\n"
                html += f"  <section><h2>{slide['title']}</h2>"
                if summary:
                    html += f"<blockquote>{summary}</blockquote>"
                html += "<p>↓</p></section>\n"
                # Recursively create HTML for child slides
                html += create_slides_html(child_slides)
                html += "</section>\n"
                i = j # Move index past the children
        return html

    slides_html = create_slides_html(slides_data)

    return f"""
<!doctype html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <title>{title}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reset.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/serif.min.css" id="theme">
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <section><h1>{title}</h1></section>
            {slides_html}
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.js"></script>
    <script>Reveal.initialize({{ hash: true }});</script>
</body>
</html>
"""

def process_document(docx_path, output_dir):
    """Main processing pipeline for a single DOCX file."""
    base_name = os.path.splitext(os.path.basename(docx_path))[0]
    output_path = os.path.join(output_dir, f"{base_name}{CONFIG['output_filename_suffix']}")
    
    print(f"\n--- Processing: {base_name}.docx ---")
    
    try:
        doc = Document(docx_path)
        
        # Use the first paragraph as the main title, or the filename if empty
        main_title = doc.paragraphs[0].text.strip() if doc.paragraphs else base_name
        
        print("  - Parsing document structure...")
        structured_slides = parse_docx_structure(doc)
        
        print("  - Generating HTML and summarizing content...")
        html_content = generate_html(main_title, structured_slides)
        
        # CRITICAL: Write with explicit UTF-8 encoding
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        print(f"  - SUCCESS: Professional slides saved to '{os.path.basename(output_path)}'")

    except Exception as e:
        print(f"  - ERROR: Failed to process {os.path.basename(docx_path)}.")
        print(traceback.format_exc())

def main():
    if not summarizer:
        return # Stop if the model failed to load

    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    docx_files = glob.glob("*.docx")
    if not docx_files:
        print("No .docx files found in this directory.")
        return

    for docx_file in docx_files:
        process_document(docx_file, script_dir)

if __name__ == "__main__":
    main()

