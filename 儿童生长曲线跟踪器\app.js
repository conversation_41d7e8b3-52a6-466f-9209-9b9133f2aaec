// 儿童生长曲线跟踪器主应用
class GrowthTracker {
    constructor() {
        this.childData = this.loadData();
        this.currentChart = null;
        this.activeChartType = 'height';
        this.init();
    }

    init() {
        this.bindEvents();
        this.renderHistory();
        this.updateAnalysis();
        // 初始化图表（如果有数据）
        if (this.childData.measurements.length > 0) {
            this.renderChart();
        }
    }

    bindEvents() {
        // 表单提交事件
        document.getElementById('childDataForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addMeasurement();
        });

        // 图表切换事件
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchChart(e.target.dataset.chart);
            });
        });

        // 设置默认日期
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('measureDate').value = today;

        // 数据管理事件
        document.getElementById('loadDemo').addEventListener('click', () => {
            this.loadDemoData();
        });

        document.getElementById('exportData').addEventListener('click', () => {
            this.exportData();
        });

        document.getElementById('clearData').addEventListener('click', () => {
            this.clearAllData();
        });

        // 图表标准切换事件
        document.getElementById('chartStandardSelect').addEventListener('change', (e) => {
            this.switchStandard(e.target.value);
        });

        // 全屏查看事件
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.openFullscreen();
        });

        // 关闭全屏事件
        document.getElementById('closeFullscreen').addEventListener('click', () => {
            this.closeFullscreen();
        });

        // 点击模态框背景关闭
        document.getElementById('fullscreenModal').addEventListener('click', (e) => {
            if (e.target.id === 'fullscreenModal') {
                this.closeFullscreen();
            }
        });

        // ESC键关闭全屏
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeFullscreen();
            }
        });
    }

    addMeasurement() {
        // 显示加载状态
        this.showLoading('正在添加数据...');

        setTimeout(() => {
            const formData = new FormData(document.getElementById('childDataForm'));
            const measurement = {
                id: Date.now(),
                birthDate: formData.get('birthDate'),
                gender: formData.get('gender'),
                standard: formData.get('standard'),
                measureDate: formData.get('measureDate'),
                height: parseFloat(formData.get('height')),
                weight: parseFloat(formData.get('weight')),
                ageInMonths: this.calculateAgeInMonths(formData.get('birthDate'), formData.get('measureDate'))
            };

            // 验证数据
            if (!this.validateMeasurement(measurement)) {
                this.hideLoading();
                return;
            }

            // 更新基本信息（如果是第一次输入）
            if (this.childData.measurements.length === 0) {
                this.childData.birthDate = measurement.birthDate;
                this.childData.gender = measurement.gender;
                this.childData.standard = measurement.standard;
            }

            // 添加测量记录
            this.childData.measurements.push(measurement);
            this.childData.measurements.sort((a, b) => new Date(a.measureDate) - new Date(b.measureDate));

            // 保存数据
            this.saveData();

            // 更新界面
            this.renderHistory();
            this.renderChart();
            this.updateAnalysis();

            // 重置表单
            document.getElementById('childDataForm').reset();
            document.getElementById('measureDate').value = new Date().toISOString().split('T')[0];

            this.hideLoading();
            this.showMessage('数据添加成功！', 'success');
        }, 500); // 模拟处理时间
    }

    validateMeasurement(measurement) {
        // 基本验证
        if (!measurement.birthDate || !measurement.gender || !measurement.standard ||
            !measurement.measureDate || !measurement.height || !measurement.weight) {
            this.showMessage('请填写完整信息', 'error');
            return false;
        }

        // 年龄验证
        if (measurement.ageInMonths < 0) {
            this.showMessage('测量日期不能早于出生日期', 'error');
            return false;
        }

        if (measurement.ageInMonths > 240) { // 20岁
            this.showMessage('年龄超出跟踪范围', 'error');
            return false;
        }

        // 身高体重合理性验证
        if (measurement.height < 30 || measurement.height > 200) {
            this.showMessage('身高数据不合理', 'error');
            return false;
        }

        if (measurement.weight < 1 || measurement.weight > 100) {
            this.showMessage('体重数据不合理', 'error');
            return false;
        }

        return true;
    }

    calculateAgeInMonths(birthDate, measureDate) {
        const birth = new Date(birthDate);
        const measure = new Date(measureDate);
        const diffTime = measure - birth;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);
        return Math.round(diffDays / 30.44); // 平均每月天数
    }

    switchChart(chartType) {
        this.activeChartType = chartType;
        
        // 更新标签页状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');

        // 重新渲染图表
        this.renderChart();
    }

    renderChart() {
        if (this.childData.measurements.length === 0) {
            const chartContainer = document.querySelector('.chart-container');
            chartContainer.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    <p>请先添加测量数据以查看生长曲线</p>
                </div>
            `;
            return;
        }

        // 检查必要数据
        if (!this.childData.gender || !this.childData.standard) {
            const chartContainer = document.querySelector('.chart-container');
            chartContainer.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #e74c3c;">
                    <p>缺少性别或标准信息，无法显示标准曲线</p>
                </div>
            `;
            return;
        }

        // 销毁现有图表
        if (this.currentChart) {
            this.currentChart.destroy();
        }

        const canvas = document.getElementById('growthChart');
        if (!canvas) {
            console.error('Canvas element not found');
            return;
        }
        const ctx = canvas.getContext('2d');

        // 准备数据
        const measurements = this.childData.measurements.sort((a, b) => a.ageInMonths - b.ageInMonths);
        const gender = this.childData.gender;
        const standard = this.getCurrentStandard();

        // 更新图表标准选择器
        document.getElementById('chartStandardSelect').value = standard;

        // 生成年龄范围（0-60个月）
        const ageRange = [];
        for (let i = 0; i <= 60; i += 3) {
            ageRange.push(i);
        }

        // 准备标准曲线数据
        const standardData = this.generateStandardCurves(ageRange, gender, this.activeChartType, standard);

        // 准备用户数据
        const userLabels = measurements.map(m => m.ageInMonths);
        const userAges = measurements.map(m => m.ageInMonths);

        let userData, yAxisLabel, userColor;

        if (this.activeChartType === 'height') {
            userData = measurements.map(m => m.height);
            yAxisLabel = '身高 (cm)';
            userColor = 'rgb(75, 192, 192)';
        } else {
            userData = measurements.map(m => m.weight);
            yAxisLabel = '体重 (kg)';
            userColor = 'rgb(255, 99, 132)';
        }

        // 创建数据集
        const datasets = [
            // WHO标准曲线
            ...standardData,
            // 用户数据
            {
                label: `孩子的${this.activeChartType === 'height' ? '身高' : '体重'}`,
                data: userAges.map((age, index) => ({x: age, y: userData[index]})),
                borderColor: userColor,
                backgroundColor: userColor.replace('rgb', 'rgba').replace(')', ', 0.2)'),
                borderWidth: 4,
                fill: false,
                tension: 0.4,
                pointBackgroundColor: userColor,
                pointBorderColor: '#fff',
                pointBorderWidth: 3,
                pointRadius: 8,
                pointHoverRadius: 10,
                order: 0 // 确保用户数据在最前面
            }
        ];

        // 创建图表
        this.currentChart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: `${this.activeChartType === 'height' ? '身高' : '体重'}生长曲线 (${standard === 'mainland' ? '中国大陆标准 (2023)' : '香港标准 (2020)'})`,
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        type: 'linear',
                        position: 'bottom',
                        title: {
                            display: true,
                            text: '年龄 (月)'
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        min: 0,
                        max: Math.max(60, Math.max(...userAges) + 6)
                    },
                    y: {
                        title: {
                            display: true,
                            text: yAxisLabel
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        beginAtZero: false
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#fff'
                    }
                }
            }
        });
    }

    generateStandardCurves(ageRange, gender, standard = 'mainland') {
        const type = this.activeChartType;
        const datasets = [];

        // 定义百分位线
        const percentiles = [
            { key: 'p3', label: '3rd', color: 'rgba(231, 76, 60, 0.6)', dash: [5, 5] },
            { key: 'p15', label: '15th', color: 'rgba(243, 156, 18, 0.6)', dash: [3, 3] },
            { key: 'p50', label: '50th (中位数)', color: 'rgba(52, 152, 219, 0.8)', dash: [] },
            { key: 'p85', label: '85th', color: 'rgba(243, 156, 18, 0.6)', dash: [3, 3] },
            { key: 'p97', label: '97th', color: 'rgba(231, 76, 60, 0.6)', dash: [5, 5] }
        ];

        percentiles.forEach(percentile => {
            const data = ageRange.map(age => {
                const standardData = getClosestStandardExtended(age, gender, type, standard);
                return standardData ? { x: age, y: standardData[percentile.key] } : null;
            }).filter(point => point !== null);

            datasets.push({
                label: percentile.label,
                data: data,
                borderColor: percentile.color,
                backgroundColor: 'transparent',
                borderWidth: 2,
                borderDash: percentile.dash,
                fill: false,
                pointRadius: 0,
                pointHoverRadius: 0,
                tension: 0.4,
                order: 1
            });
        });

        return datasets;
    }

    renderHistory() {
        const historyList = document.getElementById('historyList');
        
        if (this.childData.measurements.length === 0) {
            historyList.innerHTML = '<p>暂无记录</p>';
            return;
        }

        const historyHTML = this.childData.measurements.map(measurement => `
            <div class="history-item" style="padding: 15px; margin-bottom: 10px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #667eea;">
                <div style="display: flex; justify-content: between; align-items: center;">
                    <div>
                        <strong>${measurement.measureDate}</strong> (${measurement.ageInMonths}个月)
                        <br>
                        身高: ${measurement.height}cm | 体重: ${measurement.weight}kg
                    </div>
                    <button onclick="growthTracker.deleteMeasurement(${measurement.id})" 
                            style="background: #e74c3c; padding: 5px 10px; font-size: 12px;">删除</button>
                </div>
            </div>
        `).join('');

        historyList.innerHTML = historyHTML;
    }

    deleteMeasurement(id) {
        if (confirm('确定要删除这条记录吗？')) {
            this.childData.measurements = this.childData.measurements.filter(m => m.id !== id);
            this.saveData();
            this.renderHistory();
            this.renderChart();
            this.updateAnalysis();
            this.showMessage('记录已删除', 'success');
        }
    }

    updateAnalysis() {
        const analysisReport = document.getElementById('analysisReport');

        if (this.childData.measurements.length === 0) {
            analysisReport.innerHTML = '<p>请先添加孩子的测量数据</p>';
            return;
        }

        const latest = this.childData.measurements[this.childData.measurements.length - 1];
        const gender = this.childData.gender;

        // 计算百分位数
        const heightPercentile = calculatePercentileExtended(latest.height, latest.ageInMonths, gender, 'height', this.childData.standard);
        const weightPercentile = calculatePercentileExtended(latest.weight, latest.ageInMonths, gender, 'weight', this.childData.standard);

        // 获取评估结果
        const heightAssessment = getGrowthAssessmentExtended(heightPercentile, 'height');
        const weightAssessment = getGrowthAssessmentExtended(weightPercentile, 'weight');

        // 生长趋势分析
        const trendAnalysis = this.analyzeTrend();

        const standardName = this.childData.standard === 'mainland' ? '中国大陆标准 (2023)' : '香港标准 (2020)';

        const analysisHTML = `
            <h3>最新测量数据分析</h3>
            <div class="measurement-summary">
                <p><strong>测量日期：</strong>${latest.measureDate}</p>
                <p><strong>年龄：</strong>${latest.ageInMonths}个月 (${Math.floor(latest.ageInMonths/12)}岁${latest.ageInMonths%12}个月)</p>
                <p><strong>身高：</strong>${latest.height}cm</p>
                <p><strong>体重：</strong>${latest.weight}kg</p>
                <p><strong>参考标准：</strong>${standardName}</p>
            </div>

            <div class="percentile-analysis">
                <h4>百分位数分析</h4>
                <div class="percentile-item" style="border-left: 4px solid ${heightAssessment.color};">
                    <p><strong>身高百分位：</strong>第${heightPercentile}百分位</p>
                    <p style="color: ${heightAssessment.color};">${heightAssessment.message}</p>
                </div>
                <div class="percentile-item" style="border-left: 4px solid ${weightAssessment.color};">
                    <p><strong>体重百分位：</strong>第${weightPercentile}百分位</p>
                    <p style="color: ${weightAssessment.color};">${weightAssessment.message}</p>
                </div>
            </div>

            ${trendAnalysis}

            <div class="recommendations">
                <h4>建议</h4>
                ${this.generateRecommendations(heightAssessment, weightAssessment, latest.ageInMonths)}
            </div>
        `;

        analysisReport.innerHTML = analysisHTML;
    }

    analyzeTrend() {
        if (this.childData.measurements.length < 2) {
            return '<div class="trend-analysis"><h4>生长趋势</h4><p>需要至少两次测量数据才能分析趋势</p></div>';
        }

        const measurements = this.childData.measurements.sort((a, b) => new Date(a.measureDate) - new Date(b.measureDate));
        const latest = measurements[measurements.length - 1];
        const previous = measurements[measurements.length - 2];

        const timeDiff = (new Date(latest.measureDate) - new Date(previous.measureDate)) / (1000 * 60 * 60 * 24 * 30.44); // 月份差
        const heightGrowth = latest.height - previous.height;
        const weightGrowth = latest.weight - previous.weight;

        const monthlyHeightGrowth = heightGrowth / timeDiff;
        const monthlyWeightGrowth = weightGrowth / timeDiff;

        let trendMessage = '';
        if (monthlyHeightGrowth > 0 && monthlyWeightGrowth > 0) {
            trendMessage = '身高和体重都在稳定增长';
        } else if (monthlyHeightGrowth > 0) {
            trendMessage = '身高在增长，体重变化较小';
        } else if (monthlyWeightGrowth > 0) {
            trendMessage = '体重在增长，身高变化较小';
        } else {
            trendMessage = '生长速度较慢，建议关注';
        }

        return `
            <div class="trend-analysis">
                <h4>生长趋势 (过去${timeDiff.toFixed(1)}个月)</h4>
                <p><strong>身高增长：</strong>${heightGrowth.toFixed(1)}cm (月均${monthlyHeightGrowth.toFixed(1)}cm)</p>
                <p><strong>体重增长：</strong>${weightGrowth.toFixed(1)}kg (月均${monthlyWeightGrowth.toFixed(2)}kg)</p>
                <p><strong>趋势评估：</strong>${trendMessage}</p>
            </div>
        `;
    }

    generateRecommendations(heightAssessment, weightAssessment, ageInMonths) {
        const recommendations = [];

        // 基于身高评估的建议
        if (heightAssessment.status === 'low') {
            recommendations.push('建议咨询儿科医生，评估是否需要进一步检查');
            recommendations.push('确保充足的营养摄入，特别是蛋白质和钙质');
        } else if (heightAssessment.status === 'high') {
            recommendations.push('身高较高，注意营养均衡，避免过度营养');
        }

        // 基于体重评估的建议
        if (weightAssessment.status === 'low') {
            recommendations.push('体重偏轻，建议增加营养密度高的食物');
            recommendations.push('如果食欲不佳，建议咨询医生');
        } else if (weightAssessment.status === 'high') {
            recommendations.push('体重偏重，建议控制高热量食物摄入');
            recommendations.push('增加适当的运动量');
        }

        // 通用建议
        recommendations.push('保持规律的作息时间，充足的睡眠有助于生长');
        recommendations.push('定期记录生长数据，建议每月测量一次');

        if (ageInMonths < 24) {
            recommendations.push('2岁前是快速生长期，注意营养补充');
        }

        return recommendations.map(rec => `<li>${rec}</li>`).join('');
    }

    loadData() {
        const saved = localStorage.getItem('childGrowthData');
        return saved ? JSON.parse(saved) : {
            birthDate: '',
            gender: '',
            standard: '',
            measurements: []
        };
    }

    saveData() {
        localStorage.setItem('childGrowthData', JSON.stringify(this.childData));
    }

    exportData() {
        if (this.childData.measurements.length === 0) {
            this.showMessage('没有数据可导出', 'error');
            return;
        }

        const exportData = {
            exportDate: new Date().toISOString(),
            childInfo: {
                birthDate: this.childData.birthDate,
                gender: this.childData.gender === 'male' ? '男' : '女'
            },
            measurements: this.childData.measurements.map(m => ({
                测量日期: m.measureDate,
                年龄月数: m.ageInMonths,
                身高cm: m.height,
                体重kg: m.weight
            }))
        };

        // 创建CSV格式
        const csvContent = this.generateCSV(exportData);

        // 下载文件
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `儿童生长数据_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showMessage('数据导出成功！', 'success');
    }

    generateCSV(data) {
        let csv = '\uFEFF'; // BOM for UTF-8
        csv += '儿童生长数据导出\n\n';
        csv += `导出日期,${data.exportDate.split('T')[0]}\n`;
        csv += `出生日期,${data.childInfo.birthDate}\n`;
        csv += `性别,${data.childInfo.gender}\n\n`;
        csv += '测量日期,年龄(月),身高(cm),体重(kg)\n';

        data.measurements.forEach(m => {
            csv += `${m.测量日期},${m.年龄月数},${m.身高cm},${m.体重kg}\n`;
        });

        return csv;
    }

    loadDemoData() {
        if (this.childData.measurements.length > 0) {
            if (!confirm('当前已有数据，加载演示数据将覆盖现有数据，确定继续吗？')) {
                return;
            }
        }

        // 演示数据：一个12个月大男孩的生长记录
        const demoData = {
            birthDate: '2023-01-15',
            gender: 'male',
            standard: 'mainland',
            measurements: [
                { id: 1, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2023-01-15', height: 50.0, weight: 3.4, ageInMonths: 0 },
                { id: 2, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2023-02-15', height: 54.5, weight: 4.2, ageInMonths: 1 },
                { id: 3, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2023-03-15', height: 58.0, weight: 5.1, ageInMonths: 2 },
                { id: 4, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2023-04-15', height: 61.2, weight: 5.8, ageInMonths: 3 },
                { id: 5, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2023-05-15', height: 63.8, weight: 6.5, ageInMonths: 4 },
                { id: 6, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2023-06-15', height: 66.0, weight: 7.2, ageInMonths: 5 },
                { id: 7, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2023-07-15', height: 67.8, weight: 7.8, ageInMonths: 6 },
                { id: 8, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2023-10-15', height: 71.5, weight: 8.9, ageInMonths: 9 },
                { id: 9, birthDate: '2023-01-15', gender: 'male', standard: 'mainland', measureDate: '2024-01-15', height: 75.2, weight: 9.8, ageInMonths: 12 }
            ]
        };

        this.childData = demoData;
        this.saveData();

        // 更新界面
        this.renderHistory();
        this.renderChart();
        this.updateAnalysis();

        this.showMessage('演示数据加载成功！', 'success');
    }

    clearAllData() {
        if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
            localStorage.removeItem('childGrowthData');
            this.childData = {
                birthDate: '',
                gender: '',
                standard: '',
                measurements: []
            };

            // 重置界面
            document.getElementById('childDataForm').reset();
            document.getElementById('measureDate').value = new Date().toISOString().split('T')[0];
            this.renderHistory();
            this.updateAnalysis();

            // 清空图表
            if (this.currentChart) {
                this.currentChart.destroy();
                this.currentChart = null;
            }

            const chartContainer = document.querySelector('.chart-container');
            chartContainer.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    <p>请先添加测量数据以查看生长曲线</p>
                </div>
            `;

            this.showMessage('数据已清空', 'success');
        }
    }

    showLoading(message = '正在处理...') {
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingText = loadingOverlay.querySelector('p');
        loadingText.textContent = message;
        loadingOverlay.classList.remove('hidden');
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.classList.add('hidden');
    }

    showMessage(message, type = 'info') {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.textContent = message;
        errorDiv.className = `error-message ${type === 'success' ? 'success-message' : ''}`;
        errorDiv.classList.remove('hidden');

        // 添加滑入动画
        errorDiv.style.animation = 'slideIn 0.3s ease';

        setTimeout(() => {
            errorDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                errorDiv.classList.add('hidden');
            }, 300);
        }, 3000);
    }

    // 切换生长标准
    switchStandard(standard) {
        if (this.childData.measurements.length === 0) {
            this.showMessage('请先添加测量数据', 'error');
            return;
        }

        // 更新所有测量数据的标准
        this.childData.measurements.forEach(measurement => {
            measurement.standard = standard;
        });

        // 保存数据
        this.saveData();

        // 重新渲染图表和分析
        this.renderChart();
        this.updateAnalysis();

        this.showMessage(`已切换到${standard === 'mainland' ? '中国大陆标准' : '香港标准'}`, 'success');
    }

    // 打开全屏模式
    openFullscreen() {
        if (this.childData.measurements.length === 0) {
            this.showMessage('请先添加测量数据以查看图表', 'error');
            return;
        }

        const modal = document.getElementById('fullscreenModal');
        const title = document.getElementById('fullscreenTitle');

        // 设置标题
        const chartType = this.activeChartType === 'height' ? '身高' : '体重';
        const standard = this.getCurrentStandard();
        const standardName = standard === 'mainland' ? '中国大陆标准' : '香港标准';
        title.textContent = `${chartType}曲线图 - ${standardName} - 全屏查看`;

        // 显示模态框
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // 渲染全屏图表
        setTimeout(() => {
            this.renderFullscreenChart();
        }, 300);
    }

    // 关闭全屏模式
    closeFullscreen() {
        const modal = document.getElementById('fullscreenModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';

        // 销毁全屏图表
        if (this.fullscreenChart) {
            this.fullscreenChart.destroy();
            this.fullscreenChart = null;
        }
    }

    // 渲染全屏图表
    renderFullscreenChart() {
        // 销毁现有全屏图表
        if (this.fullscreenChart) {
            this.fullscreenChart.destroy();
        }

        const canvas = document.getElementById('fullscreenChart');
        if (!canvas) {
            console.error('Fullscreen canvas element not found');
            return;
        }
        const ctx = canvas.getContext('2d');

        // 准备数据
        const measurements = this.childData.measurements.sort((a, b) => a.ageInMonths - b.ageInMonths);
        const gender = this.childData.gender;
        const standard = this.getCurrentStandard();

        // 生成年龄范围（0-36个月）
        const ageRange = Array.from({length: 37}, (_, i) => i);

        // 生成标准曲线数据
        const standardCurves = this.generateStandardCurves(ageRange, gender, this.activeChartType, standard);

        // 准备用户数据
        const userData = measurements.map(m => ({
            x: m.ageInMonths,
            y: this.activeChartType === 'height' ? m.height : m.weight
        }));

        // 图表配置
        const config = {
            type: 'line',
            data: {
                datasets: [
                    ...standardCurves,
                    {
                        label: '孩子的数据',
                        data: userData,
                        borderColor: '#e74c3c',
                        backgroundColor: '#e74c3c',
                        borderWidth: 4,
                        pointRadius: 8,
                        pointHoverRadius: 10,
                        tension: 0.1,
                        fill: false,
                        pointBackgroundColor: '#e74c3c',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 3
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: `${this.activeChartType === 'height' ? '身高' : '体重'}生长曲线 - ${standard === 'mainland' ? '中国大陆标准 (2023)' : '香港标准 (2020)'}`,
                        font: {
                            size: 18,
                            weight: 'bold'
                        },
                        padding: 20
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 14
                            },
                            padding: 20
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '年龄 (月)',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            font: {
                                size: 14
                            }
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: this.activeChartType === 'height' ? '身高 (cm)' : '体重 (kg)',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            font: {
                                size: 14
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        };

        this.fullscreenChart = new Chart(ctx, config);
    }

    // 获取当前标准
    getCurrentStandard() {
        if (this.childData.measurements.length > 0) {
            return this.childData.measurements[0].standard || 'mainland';
        }
        return document.getElementById('chartStandardSelect').value || 'mainland';
    }
}

// 初始化应用
let growthTracker;
document.addEventListener('DOMContentLoaded', () => {
    growthTracker = new GrowthTracker();
});
