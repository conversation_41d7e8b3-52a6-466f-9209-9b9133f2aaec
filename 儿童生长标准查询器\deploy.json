{"name": "儿童生长标准查询器", "description": "基于中国大陆2023年标准的儿童生长发育查询工具", "version": "1.0.0", "type": "static", "entry": "index.html", "build": {"command": "", "output": "."}, "routes": [{"src": "/", "dest": "/index.html"}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "env": {}, "functions": {}, "regions": ["hkg1", "sin1"], "github": {"enabled": false}}