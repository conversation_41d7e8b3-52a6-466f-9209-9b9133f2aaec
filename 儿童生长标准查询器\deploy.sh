#!/bin/bash

# 儿童生长标准查询器 - Edge MCP 部署脚本

echo "🚀 开始部署儿童生长标准查询器到 Edge MCP..."

# 检查必要文件
if [ ! -f "index.html" ]; then
    echo "❌ 错误: 找不到 index.html 文件"
    exit 1
fi

if [ ! -f "deploy.json" ]; then
    echo "❌ 错误: 找不到 deploy.json 配置文件"
    exit 1
fi

echo "✅ 文件检查完成"

# 创建部署包
echo "📦 创建部署包..."
zip -r child-growth-checker.zip . -x "*.sh" "*.md" ".git/*" ".DS_Store"

echo "✅ 部署包创建完成: child-growth-checker.zip"

echo "📋 部署步骤:"
echo "1. 登录 Edge MCP 控制台"
echo "2. 创建新项目"
echo "3. 上传 child-growth-checker.zip"
echo "4. 设置项目类型为 Static Site"
echo "5. 设置入口文件为 index.html"
echo "6. 配置域名和SSL"
echo "7. 启用CDN加速"

echo ""
echo "🌐 部署完成后，您的应用将可以通过以下方式访问:"
echo "- Edge MCP 提供的默认域名"
echo "- 您配置的自定义域名"

echo ""
echo "📱 建议配置:"
echo "- 启用 HTTPS"
echo "- 配置 CDN 缓存"
echo "- 设置适当的缓存策略"
echo "- 启用 Gzip 压缩"

echo ""
echo "🎉 部署脚本执行完成！"
