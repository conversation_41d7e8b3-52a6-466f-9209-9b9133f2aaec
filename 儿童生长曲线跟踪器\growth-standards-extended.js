// 扩展的生长标准数据 - 包含中国大陆和香港标准

// 中国大陆标准 (2023年国家卫健委发布)
const mainlandStandards = {
    male: {
        height: [
            // 年龄(月), p3, p15, p50, p85, p97
            { age: 0, p3: 46.3, p15: 48.0, p50: 50.0, p85: 52.0, p97: 53.7 },
            { age: 1, p3: 50.8, p15: 52.8, p50: 54.8, p85: 56.8, p97: 58.8 },
            { age: 2, p3: 54.4, p15: 56.4, p50: 58.4, p85: 60.4, p97: 62.4 },
            { age: 3, p3: 57.3, p15: 59.4, p50: 61.4, p85: 63.5, p97: 65.5 },
            { age: 4, p3: 59.7, p15: 61.8, p50: 63.9, p85: 66.0, p97: 68.0 },
            { age: 5, p3: 61.7, p15: 63.8, p50: 65.9, p85: 68.0, p97: 70.1 },
            { age: 6, p3: 63.3, p15: 65.5, p50: 67.6, p85: 69.8, p97: 71.9 },
            { age: 9, p3: 67.7, p15: 70.1, p50: 72.3, p85: 74.5, p97: 76.9 },
            { age: 12, p3: 71.0, p15: 73.4, p50: 75.7, p85: 78.1, p97: 80.5 },
            { age: 18, p3: 76.9, p15: 79.6, p50: 82.3, p85: 85.0, p97: 87.7 },
            { age: 24, p3: 82.3, p15: 85.1, p50: 87.9, p85: 90.7, p97: 93.4 },
            { age: 36, p3: 91.9, p15: 95.0, p50: 98.1, p85: 101.2, p97: 104.3 },
            { age: 48, p3: 100.7, p15: 104.1, p50: 107.5, p85: 110.9, p97: 114.2 },
            { age: 60, p3: 108.9, p15: 112.5, p50: 116.1, p85: 119.7, p97: 123.2 }
        ],
        weight: [
            // 年龄(月), p3, p15, p50, p85, p97
            { age: 0, p3: 2.5, p15: 2.9, p50: 3.4, p85: 3.9, p97: 4.4 },
            { age: 1, p3: 3.4, p15: 3.9, p50: 4.5, p85: 5.1, p97: 5.8 },
            { age: 2, p3: 4.3, p15: 4.9, p50: 5.6, p85: 6.3, p97: 7.1 },
            { age: 3, p3: 5.0, p15: 5.7, p50: 6.4, p85: 7.2, p97: 8.0 },
            { age: 4, p3: 5.6, p15: 6.2, p50: 7.0, p85: 7.8, p97: 8.7 },
            { age: 5, p3: 6.0, p15: 6.7, p50: 7.5, p85: 8.4, p97: 9.3 },
            { age: 6, p3: 6.4, p15: 7.1, p50: 7.9, p85: 8.8, p97: 9.8 },
            { age: 9, p3: 7.1, p15: 7.9, p50: 8.9, p85: 10.0, p97: 11.2 },
            { age: 12, p3: 7.7, p15: 8.6, p50: 9.6, p85: 10.8, p97: 12.0 },
            { age: 18, p3: 8.8, p15: 9.8, p50: 11.0, p85: 12.4, p97: 13.9 },
            { age: 24, p3: 10.0, p15: 11.2, p50: 12.5, p85: 14.1, p97: 15.9 },
            { age: 36, p3: 12.1, p15: 13.6, p50: 15.3, p85: 17.3, p97: 19.6 },
            { age: 48, p3: 14.4, p15: 16.2, p50: 18.3, p85: 20.7, p97: 23.6 },
            { age: 60, p3: 16.9, p15: 19.0, p50: 21.4, p85: 24.3, p97: 27.9 }
        ]
    },
    female: {
        height: [
            { age: 0, p3: 45.4, p15: 47.3, p50: 49.1, p85: 51.0, p97: 52.9 },
            { age: 1, p3: 49.8, p15: 51.7, p50: 53.7, p85: 55.6, p97: 57.6 },
            { age: 2, p3: 53.0, p15: 55.0, p50: 57.1, p85: 59.1, p97: 61.1 },
            { age: 3, p3: 55.6, p15: 57.7, p50: 59.8, p85: 61.9, p97: 64.0 },
            { age: 4, p3: 57.8, p15: 59.9, p50: 62.1, p85: 64.3, p97: 66.4 },
            { age: 5, p3: 59.6, p15: 61.8, p50: 64.0, p85: 66.2, p97: 68.5 },
            { age: 6, p3: 61.2, p15: 63.5, p50: 65.7, p85: 68.0, p97: 70.3 },
            { age: 9, p3: 65.3, p15: 67.7, p50: 70.1, p85: 72.6, p97: 75.0 },
            { age: 12, p3: 68.9, p15: 71.4, p50: 73.9, p85: 76.4, p97: 78.9 },
            { age: 18, p3: 74.9, p15: 77.7, p50: 80.5, p85: 83.2, p97: 86.0 },
            { age: 24, p3: 80.0, p15: 83.2, p50: 86.4, p85: 89.6, p97: 92.9 },
            { age: 36, p3: 90.0, p15: 93.4, p50: 96.9, p85: 100.3, p97: 103.7 },
            { age: 48, p3: 99.1, p15: 102.7, p50: 106.4, p85: 110.0, p97: 113.6 },
            { age: 60, p3: 107.4, p15: 111.3, p50: 115.2, p85: 119.1, p97: 123.0 }
        ],
        weight: [
            { age: 0, p3: 2.4, p15: 2.8, p50: 3.2, p85: 3.7, p97: 4.2 },
            { age: 1, p3: 3.2, p15: 3.6, p50: 4.2, p85: 4.8, p97: 5.5 },
            { age: 2, p3: 3.9, p15: 4.5, p50: 5.1, p85: 5.8, p97: 6.6 },
            { age: 3, p3: 4.5, p15: 5.2, p50: 5.8, p85: 6.6, p97: 7.5 },
            { age: 4, p3: 5.0, p15: 5.7, p50: 6.4, p85: 7.3, p97: 8.2 },
            { age: 5, p3: 5.4, p15: 6.1, p50: 6.9, p85: 7.8, p97: 8.8 },
            { age: 6, p3: 5.7, p15: 6.5, p50: 7.3, p85: 8.2, p97: 9.3 },
            { age: 9, p3: 6.5, p15: 7.3, p50: 8.2, p85: 9.3, p97: 10.5 },
            { age: 12, p3: 7.0, p15: 7.9, p50: 8.9, p85: 10.1, p97: 11.5 },
            { age: 18, p3: 8.1, p15: 9.2, p50: 10.4, p85: 11.8, p97: 13.5 },
            { age: 24, p3: 9.0, p15: 10.2, p50: 11.5, p85: 13.2, p97: 15.1 },
            { age: 36, p3: 11.0, p15: 12.6, p50: 14.4, p85: 16.5, p97: 19.0 },
            { age: 48, p3: 13.3, p15: 15.2, p50: 17.4, p85: 20.0, p97: 23.0 },
            { age: 60, p3: 15.8, p15: 18.0, p50: 20.6, p85: 23.8, p97: 27.6 }
        ]
    }
};

// 香港标准 (2020年香港生长图表)
const hongkongStandards = {
    male: {
        height: [
            // 年龄(月), p3, p15, p50, p85, p97
            { age: 0, p3: 46.1, p15: 47.9, p50: 49.9, p85: 51.8, p97: 53.7 },
            { age: 1, p3: 50.7, p15: 52.8, p50: 54.7, p85: 56.7, p97: 58.6 },
            { age: 2, p3: 54.4, p15: 56.4, p50: 58.3, p85: 60.4, p97: 62.4 },
            { age: 3, p3: 57.3, p15: 59.4, p50: 61.4, p85: 63.5, p97: 65.5 },
            { age: 4, p3: 59.7, p15: 61.8, p50: 63.9, p85: 66.0, p97: 68.0 },
            { age: 5, p3: 61.7, p15: 63.8, p50: 65.9, p85: 68.0, p97: 70.1 },
            { age: 6, p3: 63.3, p15: 65.5, p50: 67.6, p85: 69.8, p97: 71.9 },
            { age: 9, p3: 67.7, p15: 70.1, p50: 72.3, p85: 74.5, p97: 76.9 },
            { age: 12, p3: 71.2, p15: 73.6, p50: 75.9, p85: 78.3, p97: 80.7 },
            { age: 18, p3: 77.2, p15: 79.9, p50: 82.6, p85: 85.3, p97: 88.0 },
            { age: 24, p3: 82.8, p15: 85.6, p50: 88.4, p85: 91.2, p97: 94.0 },
            { age: 36, p3: 92.5, p15: 95.6, p50: 98.7, p85: 101.8, p97: 104.9 },
            { age: 48, p3: 101.4, p15: 104.8, p50: 108.2, p85: 111.6, p97: 115.0 },
            { age: 60, p3: 109.7, p15: 113.3, p50: 116.9, p85: 120.5, p97: 124.1 }
        ],
        weight: [
            { age: 0, p3: 2.5, p15: 2.9, p50: 3.3, p85: 3.9, p97: 4.4 },
            { age: 1, p3: 3.4, p15: 3.9, p50: 4.5, p85: 5.1, p97: 5.8 },
            { age: 2, p3: 4.3, p15: 4.9, p50: 5.6, p85: 6.3, p97: 7.1 },
            { age: 3, p3: 5.0, p15: 5.7, p50: 6.4, p85: 7.2, p97: 8.0 },
            { age: 4, p3: 5.6, p15: 6.2, p50: 7.0, p85: 7.8, p97: 8.7 },
            { age: 5, p3: 6.0, p15: 6.7, p50: 7.5, p85: 8.4, p97: 9.3 },
            { age: 6, p3: 6.4, p15: 7.1, p50: 7.9, p85: 8.8, p97: 9.8 },
            { age: 9, p3: 7.1, p15: 7.9, p50: 8.9, p85: 10.0, p97: 11.2 },
            { age: 12, p3: 7.8, p15: 8.7, p50: 9.7, p85: 10.9, p97: 12.1 },
            { age: 18, p3: 9.0, p15: 10.0, p50: 11.2, p85: 12.6, p97: 14.1 },
            { age: 24, p3: 10.2, p15: 11.4, p50: 12.7, p85: 14.3, p97: 16.1 },
            { age: 36, p3: 12.4, p15: 13.9, p50: 15.6, p85: 17.6, p97: 19.9 },
            { age: 48, p3: 14.8, p15: 16.6, p50: 18.7, p85: 21.1, p97: 24.0 },
            { age: 60, p3: 17.4, p15: 19.5, p50: 21.9, p85: 24.8, p97: 28.4 }
        ]
    },
    female: {
        height: [
            { age: 0, p3: 45.4, p15: 47.3, p50: 49.1, p85: 51.0, p97: 52.9 },
            { age: 1, p3: 49.8, p15: 51.7, p50: 53.7, p85: 55.6, p97: 57.6 },
            { age: 2, p3: 53.0, p15: 55.0, p50: 57.1, p85: 59.1, p97: 61.1 },
            { age: 3, p3: 55.6, p15: 57.7, p50: 59.8, p85: 61.9, p97: 64.0 },
            { age: 4, p3: 57.8, p15: 59.9, p50: 62.1, p85: 64.3, p97: 66.4 },
            { age: 5, p3: 59.6, p15: 61.8, p50: 64.0, p85: 66.2, p97: 68.5 },
            { age: 6, p3: 61.2, p15: 63.5, p50: 65.7, p85: 68.0, p97: 70.3 },
            { age: 9, p3: 65.3, p15: 67.7, p50: 70.1, p85: 72.6, p97: 75.0 },
            { age: 12, p3: 69.1, p15: 71.6, p50: 74.1, p85: 76.6, p97: 79.1 },
            { age: 18, p3: 75.2, p15: 78.0, p50: 80.8, p85: 83.5, p97: 86.3 },
            { age: 24, p3: 80.5, p15: 83.7, p50: 86.9, p85: 90.1, p97: 93.4 },
            { age: 36, p3: 90.6, p15: 94.0, p50: 97.5, p85: 100.9, p97: 104.3 },
            { age: 48, p3: 99.8, p15: 103.4, p50: 107.1, p85: 110.7, p97: 114.3 },
            { age: 60, p3: 108.2, p15: 112.1, p50: 116.0, p85: 119.9, p97: 123.8 }
        ],
        weight: [
            { age: 0, p3: 2.4, p15: 2.8, p50: 3.2, p85: 3.7, p97: 4.2 },
            { age: 1, p3: 3.2, p15: 3.6, p50: 4.2, p85: 4.8, p97: 5.5 },
            { age: 2, p3: 3.9, p15: 4.5, p50: 5.1, p85: 5.8, p97: 6.6 },
            { age: 3, p3: 4.5, p15: 5.2, p50: 5.8, p85: 6.6, p97: 7.5 },
            { age: 4, p3: 5.0, p15: 5.7, p50: 6.4, p85: 7.3, p97: 8.2 },
            { age: 5, p3: 5.4, p15: 6.1, p50: 6.9, p85: 7.8, p97: 8.8 },
            { age: 6, p3: 5.7, p15: 6.5, p50: 7.3, p85: 8.2, p97: 9.3 },
            { age: 9, p3: 6.5, p15: 7.3, p50: 8.2, p85: 9.3, p97: 10.5 },
            { age: 12, p3: 7.1, p15: 8.0, p50: 9.0, p85: 10.2, p97: 11.6 },
            { age: 18, p3: 8.3, p15: 9.4, p50: 10.6, p85: 12.0, p97: 13.7 },
            { age: 24, p3: 9.2, p15: 10.4, p50: 11.7, p85: 13.4, p97: 15.3 },
            { age: 36, p3: 11.3, p15: 12.9, p50: 14.7, p85: 16.8, p97: 19.3 },
            { age: 48, p3: 13.7, p15: 15.6, p50: 17.8, p85: 20.4, p97: 23.4 },
            { age: 60, p3: 16.3, p15: 18.5, p50: 21.1, p85: 24.3, p97: 28.1 }
        ]
    }
};

// 获取标准数据的函数
function getStandardData(standard, gender, type) {
    const standards = standard === 'mainland' ? mainlandStandards : hongkongStandards;
    return standards[gender] ? standards[gender][type] : null;
}

// 获取最接近年龄的标准数据
function getClosestStandardExtended(ageInMonths, gender, type, standard = 'mainland') {
    const data = getStandardData(standard, gender, type);
    if (!data) return null;

    // 找到最接近的年龄数据点
    let closest = data[0];
    let minDiff = Math.abs(ageInMonths - data[0].age);

    for (let i = 1; i < data.length; i++) {
        const diff = Math.abs(ageInMonths - data[i].age);
        if (diff < minDiff) {
            minDiff = diff;
            closest = data[i];
        }
    }

    return closest;
}

// 计算百分位数
function calculatePercentileExtended(value, ageInMonths, gender, type, standard = 'mainland') {
    const standardData = getClosestStandardExtended(ageInMonths, gender, type, standard);
    if (!standardData) return 50;

    const percentiles = [
        { key: 'p3', value: 3 },
        { key: 'p15', value: 15 },
        { key: 'p50', value: 50 },
        { key: 'p85', value: 85 },
        { key: 'p97', value: 97 }
    ];

    // 找到值所在的百分位区间
    for (let i = 0; i < percentiles.length - 1; i++) {
        const current = percentiles[i];
        const next = percentiles[i + 1];
        
        if (value <= standardData[current.key]) {
            return current.value;
        }
        
        if (value >= standardData[current.key] && value <= standardData[next.key]) {
            // 线性插值
            const ratio = (value - standardData[current.key]) / 
                         (standardData[next.key] - standardData[current.key]);
            return current.value + ratio * (next.value - current.value);
        }
    }

    return value > standardData.p97 ? 97 : 3;
}

// 获取生长评估
function getGrowthAssessmentExtended(percentile, type) {
    let status, message, color;
    
    if (percentile < 3) {
        status = 'low';
        message = `${type === 'height' ? '身高' : '体重'}偏低，建议咨询医生`;
        color = '#e74c3c';
    } else if (percentile < 15) {
        status = 'below_normal';
        message = `${type === 'height' ? '身高' : '体重'}略低于平均水平`;
        color = '#f39c12';
    } else if (percentile <= 85) {
        status = 'normal';
        message = `${type === 'height' ? '身高' : '体重'}正常`;
        color = '#27ae60';
    } else if (percentile <= 97) {
        status = 'above_normal';
        message = `${type === 'height' ? '身高' : '体重'}略高于平均水平`;
        color = '#f39c12';
    } else {
        status = 'high';
        message = `${type === 'height' ? '身高' : '体重'}偏高，建议咨询医生`;
        color = '#e74c3c';
    }
    
    return { status, message, color };
}
