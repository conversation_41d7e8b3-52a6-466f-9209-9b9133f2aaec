# transform_html.py
import os
from bs4 import BeautifulSoup

# Data from the original HTML's script
formula_data = [
    {"age": 0, "range": "240 - 720 mL"},
    {"age": 1, "range": "540 - 960 mL"},
    {"age": 2, "range": "600 - 1170 mL"},
    {"age": 3, "range": "600 - 1170 mL"},
    {"age": 4, "range": "600 - 1080 mL"},
    {"age": 5, "range": "600 - 1080 mL"},
    {"age": 6, "range": "720 - 960 mL"},
    {"age": 7, "range": "720 - 960 mL"},
    {"age": 8, "range": "720 - 960 mL"},
    {"age": 9, "range": "840 - 960+ mL"},
    {"age": 10, "range": "840 - 960+ mL"},
    {"age": 11, "range": "840 - 960+ mL"},
    {"age": 12, "range": "630 - 960 mL"}
]

first_foods = [
    {'name': '鸡肉泥', 'icon': '🍗', 'description': '优质蛋白质和高生物利用率的血红素铁的极佳来源，支持肌肉和血液健康。', 'highlights': '高吸收率铁、优质蛋白'},
    {'name': '牛油果', 'icon': '��', 'description': '富含健康的单不饱和脂肪，对宝宝的大脑和神经系统发育至关重要。', 'highlights': '健康脂肪、促进大脑发育'},
    {'name': '红薯', 'icon': '🍠', 'description': 'β-胡萝卜素（维生素A）的极佳来源，支持视力和免疫系统，同时富含维生素C。', 'highlights': '维生素A、维生素C'},
    {'name': '强化铁米粉', 'icon': '🌾', 'description': '作为铁的强化载体，是预防缺铁性贫血的传统首选辅食之一。', 'highlights': '强化铁、易于消化'},
    {'name': '香蕉', 'icon': '🍌', 'description': '提供钾和维生素B6，质地柔软，味道香甜，是宝宝易于接受的水果。', 'highlights': '钾、维生素B6、易于接受'}
]

accordion_data = [
    {
        "title": "宝宝体重增长慢怎么办？",
        "content": "首先，请咨询儿科医生进行专业评估。常见原因包括：无效的乳汁转移（衔乳姿势不当）、喂养频率不足（未按需喂养，每天应达8-12次）、或母亲乳汁供应问题。管理策略包括：改善喂养姿势、增加喂养频率。在医生指导下，有时可能���要补充喂养。"
    },
    {
        "title": "如何判断是否过度喂养？",
        "content": "过度喂养在瓶喂宝宝中更常见。迹象包括：喂食后烦躁不安、胀气、频繁大量的吐奶（非普通溢奶）、体重增长过快。预防的关键是“响应式喂养”：学习并尊重宝宝的饱腹信号（如转头、闭嘴）。采用“慢速瓶喂法”能有效帮助宝宝控制进食节奏。"
    },
    {
        "title": "什么时候可以引入牛奶？(根据2023 WHO新指南)",
        "content": "对于6-11个月的非母乳喂养宝宝，在保证辅食营养全面、富含铁质的前提下，可以将全脂牛奶作为配方奶的替代选项之一。关键在于，牛奶不能取代营养丰富的辅食，尤其是富含铁的食物。对于1岁以上幼儿，WHO推荐直接饮用动物奶，而非“成长配方奶”。"
    },
    {
        "title": "如何引入花生、鸡蛋等易过敏食物？",
        "content": "与旧观念相反，新研究表明在宝宝约6个月大时尽早引入常见致敏食物，可能有助于降低过敏风险。原则是：一次只引入���种新食物，少量开始，连续观察3-5天，看有无皮疹、呕吐、腹泻等不良反应。如果没有，可逐渐增加并纳入日常饮食。对于有严重湿疹或已知食物过敏史的宝宝，请在医生指导下进行。"
    }
]

# Get the absolute path of the script
script_dir = os.path.dirname(os.path.abspath(__file__))
html_file_path = os.path.join(script_dir, '婴儿喂养与体重增长互动指南.html')
output_html_file_path = os.path.join(script_dir, '婴儿喂养与体重增长互动指南_static.html')


with open(html_file_path, 'r', encoding='utf-8') as f:
    soup = BeautifulSoup(f, 'html.parser')

# 1. Replace formula slider with a static table
formula_section = soup.find('section', id='formula-feeding')
if formula_section:
    slider_container = formula_section.find('div', class_='max-w-md')
    if slider_container:
        # Find the parent of the slider container to replace it
        container_to_replace = slider_container.find_parent('div', class_='bg-white')
        if container_to_replace:
            # Create the new content for the entire section card
            new_content_html = f"""
            <div class="bg-white rounded-xl shadow-lg p-6 md:p-8">
                <h3 class="text-2xl font-bold text-center text-teal-700 mb-2">配方奶喂养量参考</h3>
                <p class="text-center text-slate-500 text-sm mb-6">以下为各月龄宝宝的大致每日总奶量参考。请记住，关键是响应宝宝的饥饿和饱腹信号。</p>
                <div class="max-w-2xl mx-auto">
                    <table class="w-full text-sm text-left text-slate-500">
                        <thead class="text-xs text-slate-700 uppercase bg-teal-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 rounded-l-lg">宝宝月龄</th>
                                <th scope="col" class="px-6 py-3 rounded-r-lg">每日建议总奶量</th>
                            </tr>
                        </thead>
                        <tbody>
            """
            for item in formula_data:
                age_text = "新��儿" if item['age'] == 0 else f"{item['age']} 个月"
                new_content_html += f"""
                        <tr class="bg-white border-b">
                            <th scope="row" class="px-6 py-4 font-medium text-slate-900 whitespace-nowrap">{age_text}</th>
                            <td class="px-6 py-4">{item['range']}</td>
                        </tr>
                """
            new_content_html += """
                        </tbody>
                    </table>
                    <p class="text-xs text-slate-500 mt-4 text-center">通用估算法则：约150 mL/kg/天。每日最高一般不超过960 mL。</p>
                </div>
                <div class="mt-8 border-t pt-6">
                    <h4 class="text-xl font-bold text-center text-teal-700 mb-3">预防过度喂养小贴士</h4>
                    <p class="text-center text-sm text-slate-600">瓶喂时，采用“慢速瓶喂法”：让宝宝近乎直立，奶瓶保持水平，让宝宝���导吸吮节奏，喂几分钟后可暂停拍嗝，给宝宝感知饱腹感的机会。切勿强迫宝宝喝完最后一滴奶。</p>
                </div>
            </div>
            """
            container_to_replace.replace_with(BeautifulSoup(new_content_html, 'html.parser'))


# 2. Replace food cards with static, detailed cards
food_grid = soup.find('div', id='food-grid')
if food_grid:
    food_grid.clear() # Remove placeholder content
    for food in first_foods:
        card_html = f"""
        <div class="bg-amber-50 border border-amber-200 rounded-lg p-4 text-center flex flex-col h-full">
            <div class="text-5xl mb-3">{food['icon']}</div>
            <h4 class="font-bold text-slate-800">{food['name']}</h4>
            <div class="text-sm text-slate-600 mt-2 text-left flex-grow">
                <p>{food['description']}</p>
                <p class="font-semibold mt-2">关键营养亮点: <span class="font-normal text-teal-800">{food['highlights']}</span></p>
            </div>
        </div>
        """
        food_grid.append(BeautifulSoup(card_html, 'html.parser'))
    food_grid.attrs['class'] = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'


# 3. Expand accordion items
accordion_container = soup.find('div', id='accordion')
if accordion_container:
    accordion_container.clear()
    for item in accordion_data:
        accordion_item_html = f"""
        <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-4">
            <div class="w-full flex justify-between items-center text-left p-4 md:p-5 font-medium text-slate-800 bg-slate-50 border-b">
                <span>{item['title']}</span>
                <svg class="w-5 h-5 shrink-0 text-teal-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="p-4 md:p-5">
                <p class="text-slate-600">{item['content']}</p>
            </div>
        </div>
        """
        accordion_container.append(BeautifulSoup(accordion_item_html, 'html.parser'))


# 4. Remove the script tag and the modal
script_tag = soup.find('script')
if script_tag:
    script_tag.decompose()

modal = soup.find('div', id='food-modal')
if modal:
    modal.decompose()

# 5. Remove mobile menu button
mobile_menu_button = soup.find('button', id='mobile-menu-button')
if mobile_menu_button:
    mobile_menu_button.decompose()

with open(output_html_file_path, 'w', encoding='utf-8') as f:
    f.write(str(soup.prettify()))

print(f"Successfully created static HTML file: {output_html_file_path}")
