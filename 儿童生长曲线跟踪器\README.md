# 儿童生长曲线跟踪器

一个基于HTML5的儿童生长曲线跟踪应用，帮助家长记录和分析孩子的生长发育情况。

## 功能特点

### 📊 核心功能
- **数据输入**: 记录孩子的出生日期、性别、身高、体重
- **生长曲线**: 基于WHO标准的身高体重生长曲线图
- **智能分析**: 自动计算百分位数，评估生长状况
- **趋势分析**: 分析生长趋势，提供个性化建议

### 📱 用户体验
- **响应式设计**: 完美适配手机、平板、电脑
- **动画效果**: 流畅的界面动画和交互反馈
- **本地存储**: 数据安全保存在本地浏览器
- **数据导出**: 支持CSV格式数据导出

### 🔬 专业标准
- **WHO标准**: 基于世界卫生组织儿童生长标准
- **百分位计算**: 精确的百分位数计算和评估
- **年龄范围**: 支持0-60个月儿童数据跟踪

## 使用方法

### 1. 添加数据
1. 输入孩子的出生日期和性别
2. 选择测量日期
3. 输入身高和体重数据
4. 点击"添加记录"

### 2. 查看分析
- **生长曲线**: 切换查看身高或体重曲线
- **分析报告**: 查看最新的生长分析和建议
- **历史记录**: 浏览所有测量记录

### 3. 数据管理
- **导出数据**: 将数据导出为CSV文件
- **清空数据**: 清除所有本地数据

## 技术实现

### 前端技术
- **HTML5**: 语义化标记和现代Web标准
- **CSS3**: 响应式布局和动画效果
- **JavaScript ES6+**: 现代JavaScript特性
- **Chart.js**: 专业图表库

### 数据标准
- **WHO生长标准**: 0-60个月儿童身高体重标准
- **百分位数据**: 3rd, 15th, 50th, 85th, 97th百分位
- **性别区分**: 男女儿童分别的生长标准

### 存储方案
- **localStorage**: 浏览器本地存储
- **JSON格式**: 结构化数据存储
- **数据验证**: 完整的输入验证机制

## 文件结构

```
儿童生长曲线跟踪器/
├── index.html              # 主页面
├── styles.css              # 样式文件
├── app.js                  # 主应用逻辑
├── growth-standards.js     # WHO生长标准数据
└── README.md              # 说明文档
```

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 使用建议

### 数据记录
- 建议每月记录一次数据
- 保持测量条件一致（如时间、状态）
- 如有异常数据，建议咨询医生

### 结果解读
- 百分位数在15-85之间为正常范围
- 持续的生长趋势比单次数据更重要
- 如有疑虑，请咨询专业儿科医生

## 免责声明

本应用仅供参考，不能替代专业医疗建议。如对孩子的生长发育有疑虑，请及时咨询儿科医生。

## 开发信息

- **版本**: 1.0.0
- **开发时间**: 2024年
- **技术支持**: HTML5 + Chart.js + WHO标准数据

---

💡 **提示**: 首次使用时，建议添加多个历史数据点以获得更准确的趋势分析。
