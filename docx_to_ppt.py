import os
import glob
import traceback
from docx import Document
from pptx import Presentation
from pptx.util import Inches

def create_presentation_from_docx(docx_path, pptx_path):
    """
    Creates a PowerPoint presentation from a Word document.

    Each paragraph in the Word document becomes a new slide in the PowerPoint presentation.
    The first paragraph is treated as the title slide's title.
    Subsequent paragraphs become titles of their own slides.

    Args:
        docx_path (str): The absolute path to the input .docx file.
        pptx_path (str): The absolute path for the output .pptx file.
    """
    try:
        document = Document(docx_path)
        prs = Presentation()

        # Use a clean title and a flag for the first slide
        is_first_slide = True

        for para in document.paragraphs:
            # Ignore empty paragraphs
            if not para.text.strip():
                continue

            if is_first_slide:
                # Use layout 0 for the title slide
                slide_layout = prs.slide_layouts[0]
                slide = prs.slides.add_slide(slide_layout)
                title = slide.shapes.title
                if len(slide.placeholders) > 1:
                    subtitle = slide.placeholders[1]
                    subtitle.text = "" # You can add subtitle logic here if needed
                title.text = para.text
                is_first_slide = False
            else:
                # Use layout 5 for "Title Only" slides for subsequent paragraphs
                slide_layout = prs.slide_layouts[5]
                slide = prs.slides.add_slide(slide_layout)
                title = slide.shapes.title
                title.text = para.text

        prs.save(pptx_path)
        print(f"Successfully created: {os.path.abspath(pptx_path)}")

    except Exception as e:
        print(f"Error converting {os.path.basename(docx_path)}: {e}")
        print(traceback.format_exc())

def main():
    """
    Finds all .docx files in the script's directory and converts them to .pptx.
    """
    # Get the directory where the script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Searching for .docx files in: {script_dir}")

    # Change the current working directory to the script's directory
    os.chdir(script_dir)

    docx_files = glob.glob("*.docx")

    if not docx_files:
        print("No .docx files found in the current directory.")
        return

    print(f"Found files: {docx_files}")

    for docx_filename in docx_files:
        # Create full absolute paths to be safe
        abs_docx_path = os.path.join(script_dir, docx_filename)
        
        # Create the output filename
        base_name = os.path.splitext(docx_filename)[0]
        pptx_filename = f"{base_name}.pptx"
        abs_pptx_path = os.path.join(script_dir, pptx_filename)

        print(f"\nConverting '{docx_filename}' to '{pptx_filename}'...")
        create_presentation_from_docx(abs_docx_path, abs_pptx_path)

if __name__ == "__main__":
    main()