<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>儿童生长曲线跟踪器</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="growth-standards.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>儿童生长曲线跟踪器</h1>
            <p>记录孩子的成长，关注健康发育</p>
        </header>

        <main>
            <!-- 数据输入区域 -->
            <section class="input-section">
                <h2>输入孩子信息</h2>
                <form id="childDataForm">
                    <div class="form-group">
                        <label for="birthDate">出生日期：</label>
                        <input type="date" id="birthDate" name="birthDate" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="gender">性别：</label>
                        <select id="gender" name="gender" required>
                            <option value="">请选择</option>
                            <option value="male">男孩</option>
                            <option value="female">女孩</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="standard">生长标准：</label>
                        <select id="standard" name="standard" required>
                            <option value="">请选择</option>
                            <option value="mainland">中国大陆标准 (2023)</option>
                            <option value="hongkong">香港标准 (2020)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="measureDate">测量日期：</label>
                        <input type="date" id="measureDate" name="measureDate" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="height">身高 (cm)：</label>
                        <input type="number" id="height" name="height" min="30" max="200" step="0.1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="weight">体重 (kg)：</label>
                        <input type="number" id="weight" name="weight" min="1" max="100" step="0.1" required>
                    </div>
                    
                    <button type="submit">添加记录</button>
                </form>
            </section>

            <!-- 图表显示区域 -->
            <section class="chart-section">
                <div class="chart-header">
                    <h2>生长曲线图</h2>
                    <div class="chart-controls">
                        <div class="standard-switch">
                            <label>标准：</label>
                            <select id="chartStandardSelect">
                                <option value="mainland">中国大陆标准 (2023)</option>
                                <option value="hongkong">香港标准 (2020)</option>
                            </select>
                        </div>
                        <button id="fullscreenBtn" class="fullscreen-btn" title="全屏查看">
                            <span>🔍</span> 放大查看
                        </button>
                    </div>
                </div>
                <div class="chart-tabs">
                    <button class="tab-btn active" data-chart="height">身高曲线</button>
                    <button class="tab-btn" data-chart="weight">体重曲线</button>
                </div>
                <div class="chart-container">
                    <canvas id="growthChart"></canvas>
                </div>
            </section>

            <!-- 分析报告区域 -->
            <section class="analysis-section">
                <h2>生长分析报告</h2>
                <div id="analysisReport" class="report-content">
                    <p>请先添加孩子的测量数据</p>
                </div>
            </section>

            <!-- 历史记录区域 -->
            <section class="history-section">
                <div class="section-header">
                    <h2>历史记录</h2>
                    <div class="history-actions">
                        <button id="loadDemo" class="demo-btn">加载演示数据</button>
                        <button id="exportData" class="export-btn">导出数据</button>
                        <button id="clearData" class="clear-btn">清空数据</button>
                    </div>
                </div>
                <div id="historyList" class="history-list">
                    <p>暂无记录</p>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2024 儿童生长曲线跟踪器 - 关爱孩子健康成长</p>
        </footer>
    </div>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-spinner"></div>
        <p>正在分析数据...</p>
    </div>

    <!-- 错误提示 -->
    <div id="errorMessage" class="error-message hidden"></div>

    <!-- 全屏图表模态框 -->
    <div id="fullscreenModal" class="fullscreen-modal">
        <div class="fullscreen-content">
            <div class="fullscreen-header">
                <h3 id="fullscreenTitle">生长曲线图 - 全屏查看</h3>
                <button id="closeFullscreen" class="close-btn">&times;</button>
            </div>
            <div class="fullscreen-chart-container">
                <canvas id="fullscreenChart"></canvas>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="growth-standards-extended.js"></script>
    <script src="app.js"></script>
</body>
</html>
