<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>儿童生长标准查询器 - 中国大陆标准2023</title>
    <meta name="description" content="基于中国大陆2023年标准的儿童生长发育查询工具，帮助新手父母快速了解孩子的成长情况">
    <meta name="keywords" content="儿童生长,身高体重,生长标准,中国大陆,2023,新手父母">
    <meta name="author" content="儿童生长标准查询器">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="生长查询">
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMTkyIiByeD0iMjQiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxwYXRoIGQ9Ik05NiA0OEM4MS4yIDQ4IDY5LjMzMzMgNTkuODY2NyA2OS4zMzMzIDc0LjY2NjdWMTE3LjMzM0M2OS4zMzMzIDEzMi4xMzMgODEuMiAxNDQgOTYgMTQ0UzEyMi42NjcgMTMyLjEzMyAxMjIuNjY3IDExNy4zMzNWNzQuNjY2N0MxMjIuNjY3IDU5Ljg2NjcgMTEwLjggNDggOTYgNDhaIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSI4NCIgY3k9IjY4IiByPSI0IiBmaWxsPSIjNjY3ZWVhIi8+CjxjaXJjbGUgY3g9IjEwOCIgY3k9IjY4IiByPSI0IiBmaWxsPSIjNjY3ZWVhIi8+CjxwYXRoIGQ9Ik04NCA4NEM4NCA4Ny4zMTM3IDg2LjY4NjMgOTAgOTAgOTBIMTAyQzEwNS4zMTQgOTAgMTA4IDg3LjMxMzcgMTA4IDg0IiBzdHJva2U9IiM2NjdlZWEiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50MF9saW5lYXJfMV8xIiB4MT0iMCIgeTE9IjAiIHgyPSIxOTIiIHkyPSIxOTIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KPHN0b3Agc3RvcC1jb2xvcj0iIzY2N2VlYSIvPgo8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM3NjRiYTIiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .form-section {
            padding: 30px 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .check-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 16px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .check-btn:hover {
            transform: translateY(-2px);
        }

        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
        }

        .result.normal {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }

        .result.warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }

        .result.danger {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }

        .percentile-info {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            color: #666;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 12px;
            background: #f8f9fa;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px;
                max-width: none;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .form-section {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧒 儿童生长标准查询</h1>
            <p>基于中国大陆标准 (2023年)</p>
        </div>

        <div class="form-section">
            <form id="checkForm">
                <div class="form-group">
                    <label for="birthDate">👶 出生日期</label>
                    <input type="date" id="birthDate" required>
                </div>

                <div class="form-group">
                    <label for="gender">👦👧 性别</label>
                    <select id="gender" required>
                        <option value="">请选择</option>
                        <option value="male">男孩</option>
                        <option value="female">女孩</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="height">📏 身高 (cm)</label>
                    <input type="number" id="height" min="30" max="200" step="0.1" placeholder="例如: 75.5" required>
                </div>

                <div class="form-group">
                    <label for="weight">⚖️ 体重 (kg)</label>
                    <input type="number" id="weight" min="1" max="100" step="0.1" placeholder="例如: 10.2" required>
                </div>

                <button type="submit" class="check-btn">🔍 立即查询</button>
            </form>

            <div id="result" class="result hidden"></div>
        </div>

        <div class="footer">
            <p>💡 数据仅供参考，如有疑问请咨询儿科医生</p>
        </div>
    </div>

    <script>
        // 中国大陆儿童生长标准数据 (2023年)
        const CHINA_STANDARDS = {
            height_male: {
                0: { p3: 46.3, p15: 48.2, p50: 50.1, p85: 52.0, p97: 53.9 },
                1: { p3: 51.0, p15: 53.0, p50: 54.9, p85: 56.9, p97: 58.8 },
                2: { p3: 54.6, p15: 56.6, p50: 58.6, p85: 60.6, p97: 62.6 },
                3: { p3: 57.5, p15: 59.6, p50: 61.6, p85: 63.7, p97: 65.7 },
                6: { p3: 63.5, p15: 65.7, p50: 67.8, p85: 70.0, p97: 72.1 },
                9: { p3: 67.9, p15: 70.3, p50: 72.2, p85: 74.4, p97: 76.7 },
                12: { p3: 71.2, p15: 73.6, p50: 75.9, p85: 78.3, p97: 80.7 },
                18: { p3: 77.1, p15: 79.8, p50: 82.5, p85: 85.3, p97: 88.0 },
                24: { p3: 81.9, p15: 85.0, p50: 88.0, p85: 91.1, p97: 94.1 },
                36: { p3: 90.2, p15: 93.8, p50: 97.4, p85: 101.0, p97: 104.7 },
                48: { p3: 97.7, p15: 101.6, p50: 105.6, p85: 109.6, p97: 113.7 },
                60: { p3: 104.8, p15: 109.1, p50: 113.4, p85: 117.8, p97: 122.1 }
            },
            height_female: {
                0: { p3: 45.6, p15: 47.5, p50: 49.3, p85: 51.2, p97: 53.1 },
                1: { p3: 50.2, p15: 52.2, p50: 53.9, p85: 55.8, p97: 57.8 },
                2: { p3: 53.2, p15: 55.2, p50: 57.3, p85: 59.3, p97: 61.3 },
                3: { p3: 55.8, p15: 57.9, p50: 60.0, p85: 62.1, p97: 64.2 },
                6: { p3: 61.4, p15: 63.7, p50: 65.9, p85: 68.2, p97: 70.5 },
                9: { p3: 65.5, p15: 67.9, p50: 70.3, p85: 72.8, p97: 75.2 },
                12: { p3: 69.1, p15: 71.6, p50: 74.2, p85: 76.8, p97: 79.4 },
                18: { p3: 75.5, p15: 78.2, p50: 80.9, p85: 83.7, p97: 86.4 },
                24: { p3: 81.0, p15: 83.9, p50: 86.8, p85: 89.8, p97: 92.7 },
                36: { p3: 90.5, p15: 93.8, p50: 97.1, p85: 100.5, p97: 103.8 },
                48: { p3: 99.1, p15: 102.7, p50: 106.3, p85: 109.9, p97: 113.5 },
                60: { p3: 107.2, p15: 111.1, p50: 114.9, p85: 118.7, p97: 122.6 }
            },
            weight_male: {
                0: { p3: 2.5, p15: 2.9, p50: 3.3, p85: 3.8, p97: 4.3 },
                1: { p3: 3.4, p15: 4.0, p50: 4.5, p85: 5.1, p97: 5.8 },
                2: { p3: 4.3, p15: 5.0, p50: 5.6, p85: 6.3, p97: 7.1 },
                3: { p3: 5.0, p15: 5.7, p50: 6.4, p85: 7.2, p97: 8.0 },
                6: { p3: 6.4, p15: 7.3, p50: 8.2, p85: 9.2, p97: 10.2 },
                9: { p3: 7.6, p15: 8.6, p50: 9.6, p85: 10.7, p97: 11.8 },
                12: { p3: 8.7, p15: 9.7, p50: 10.8, p85: 12.0, p97: 13.3 },
                18: { p3: 10.5, p15: 11.7, p50: 12.9, p85: 14.3, p97: 15.7 },
                24: { p3: 11.9, p15: 13.2, p50: 14.6, p85: 16.1, p97: 17.8 },
                36: { p3: 14.2, p15: 15.7, p50: 17.4, p85: 19.2, p97: 21.2 },
                48: { p3: 16.3, p15: 18.1, p50: 20.0, p85: 22.1, p97: 24.4 },
                60: { p3: 18.3, p15: 20.3, p50: 22.5, p85: 24.9, p97: 27.6 }
            },
            weight_female: {
                0: { p3: 2.4, p15: 2.8, p50: 3.2, p85: 3.7, p97: 4.2 },
                1: { p3: 3.2, p15: 3.8, p50: 4.2, p85: 4.8, p97: 5.5 },
                2: { p3: 3.9, p15: 4.5, p50: 5.1, p85: 5.8, p97: 6.6 },
                3: { p3: 4.5, p15: 5.2, p50: 5.8, p85: 6.6, p97: 7.5 },
                6: { p3: 5.7, p15: 6.5, p50: 7.3, p85: 8.2, p97: 9.3 },
                9: { p3: 6.6, p15: 7.5, p50: 8.5, p85: 9.6, p97: 10.9 },
                12: { p3: 7.5, p15: 8.5, p50: 9.5, p85: 10.8, p97: 12.1 },
                18: { p3: 9.0, p15: 10.2, p50: 11.5, p85: 12.9, p97: 14.5 },
                24: { p3: 10.4, p15: 11.7, p50: 13.2, p85: 14.8, p97: 16.5 },
                36: { p3: 12.6, p15: 14.1, p50: 15.8, p85: 17.7, p97: 19.9 },
                48: { p3: 14.6, p15: 16.4, p50: 18.4, p85: 20.7, p97: 23.3 },
                60: { p3: 16.5, p15: 18.6, p50: 20.9, p85: 23.6, p97: 26.6 }
            }
        };

        function calculateAge(birthDate) {
            const birth = new Date(birthDate);
            const now = new Date();
            const diffTime = Math.abs(now - birth);
            const diffMonths = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 30.44));
            return diffMonths;
        }

        function findClosestAge(ageInMonths, standards) {
            const ages = Object.keys(standards).map(Number).sort((a, b) => a - b);
            
            if (ageInMonths <= ages[0]) return ages[0];
            if (ageInMonths >= ages[ages.length - 1]) return ages[ages.length - 1];
            
            for (let i = 0; i < ages.length - 1; i++) {
                if (ageInMonths >= ages[i] && ageInMonths <= ages[i + 1]) {
                    return Math.abs(ageInMonths - ages[i]) <= Math.abs(ageInMonths - ages[i + 1]) 
                        ? ages[i] : ages[i + 1];
                }
            }
            return ages[0];
        }

        function getPercentile(value, standards) {
            if (value < standards.p3) return "< 3rd";
            if (value < standards.p15) return "3rd-15th";
            if (value < standards.p50) return "15th-50th";
            if (value < standards.p85) return "50th-85th";
            if (value < standards.p97) return "85th-97th";
            return "> 97th";
        }

        function analyzeGrowth(height, weight, gender, ageInMonths) {
            const heightStandards = CHINA_STANDARDS[`height_${gender}`];
            const weightStandards = CHINA_STANDARDS[`weight_${gender}`];
            
            const closestAge = findClosestAge(ageInMonths, heightStandards);
            const heightStd = heightStandards[closestAge];
            const weightStd = weightStandards[closestAge];
            
            const heightPercentile = getPercentile(height, heightStd);
            const weightPercentile = getPercentile(weight, weightStd);
            
            let status = "normal";
            let message = "";
            
            if (heightPercentile === "< 3rd" || weightPercentile === "< 3rd") {
                status = "danger";
                message = "⚠️ 孩子的生长指标偏低，建议咨询儿科医生";
            } else if (heightPercentile === "> 97th" || weightPercentile === "> 97th") {
                status = "warning";
                message = "📈 孩子的生长指标偏高，建议关注饮食和运动";
            } else if (heightPercentile.includes("3rd-15th") || weightPercentile.includes("3rd-15th")) {
                status = "warning";
                message = "📊 孩子的生长指标略低于平均水平，建议关注营养";
            } else {
                status = "normal";
                message = "✅ 孩子的生长发育正常，继续保持！";
            }
            
            return {
                status,
                message,
                heightPercentile,
                weightPercentile,
                ageInMonths: Math.round(ageInMonths)
            };
        }

        document.getElementById('checkForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const birthDate = document.getElementById('birthDate').value;
            const gender = document.getElementById('gender').value;
            const height = parseFloat(document.getElementById('height').value);
            const weight = parseFloat(document.getElementById('weight').value);
            
            const ageInMonths = calculateAge(birthDate);
            const result = analyzeGrowth(height, weight, gender, ageInMonths);
            
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${result.status}`;
            resultDiv.innerHTML = `
                <div style="font-size: 18px; margin-bottom: 10px;">${result.message}</div>
                <div class="percentile-info">
                    <strong>详细分析：</strong><br>
                    年龄：${Math.floor(result.ageInMonths / 12)}岁${result.ageInMonths % 12}个月<br>
                    身高百分位：${result.heightPercentile}<br>
                    体重百分位：${result.weightPercentile}
                </div>
            `;
            resultDiv.classList.remove('hidden');
        });

        // 设置默认日期为1年前
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
        document.getElementById('birthDate').value = oneYearAgo.toISOString().split('T')[0];

        // PWA 支持
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // 安装提示
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // 可以在这里显示安装按钮
            const installBtn = document.createElement('button');
            installBtn.textContent = '📱 安装应用';
            installBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #667eea;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 25px;
                font-size: 14px;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
            `;

            installBtn.addEventListener('click', () => {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                        installBtn.remove();
                    }
                    deferredPrompt = null;
                });
            });

            document.body.appendChild(installBtn);
        });
    </script>
</body>
</html>
