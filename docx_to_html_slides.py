
import os
import glob
import traceback
from docx import Document

def is_heading(paragraph):
    """
    Determines if a paragraph is likely a heading.
    This is a heuristic: it checks if the style name contains 'Heading'
    or if the paragraph is short and bold.
    """
    style_name = paragraph.style.name
    if 'Heading' in style_name or '标题' in style_name:
        return True
    
    # Heuristic for paragraphs that are just bolded to act as titles
    if len(paragraph.text.strip()) < 50 and all(run.bold for run in paragraph.runs if run.text.strip()):
        return True
        
    return False

def generate_revealjs_html(title, slides):
    """
    Generates the full HTML for a Reveal.js presentation.

    Args:
        title (str): The main title of the presentation.
        slides (list): A list of dictionaries, where each dict has a 'title' and 'content' (list of strings).
    """
    
    slides_html = ""
    # First slide is the main title
    slides_html += f"<section><h1>{title}</h1></section>\n"

    # Add content slides
    for slide in slides:
        slide_content_html = f"<h2>{slide['title']}</h2>\n"
        if slide['content']:
            slide_content_html += "<ul>\n"
            for point in slide['content']:
                slide_content_html += f"<li>{point}</li>\n"
            slide_content_html += "</ul>\n"
        
        slides_html += f"<section>\n{slide_content_html}</section>\n"

    return f"""
<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <title>{title}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reset.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/white.min.css" id="theme">
    </head>
    <body>
        <div class="reveal">
            <div class="slides">
                {slides_html}
            </div>
        </div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.js"></script>
        <script>
            Reveal.initialize({{
                hash: true,
                plugins: []
            }});
        </script>
    </body>
</html>
"""

def convert_docx_to_html_slides(docx_path, html_path):
    """
    Reads a .docx file and converts it into a Reveal.js HTML presentation.
    """
    try:
        document = Document(docx_path)
        slides = []
        current_slide = None
        main_title = "Presentation" # Default title

        if document.paragraphs:
            main_title = document.paragraphs[0].text

        for para in document.paragraphs[1:]:
            if not para.text.strip():
                continue

            if is_heading(para):
                if current_slide:
                    slides.append(current_slide)
                current_slide = {'title': para.text, 'content': []}
            elif current_slide:
                current_slide['content'].append(para.text)
        
        if current_slide:
            slides.append(current_slide)

        html_content = generate_revealjs_html(main_title, slides)
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        print(f"Successfully created HTML slides: {os.path.basename(html_path)}")

    except Exception as e:
        print(f"Error converting {os.path.basename(docx_path)}: {e}")
        print(traceback.format_exc())


def main():
    """
    Main function to convert all .docx files to HTML slides.
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    docx_files = glob.glob("*.docx")
    if not docx_files:
        print("No .docx files found.")
        return

    print(f"Found .docx files: {docx_files}")
    for docx_filename in docx_files:
        base_name = os.path.splitext(docx_filename)[0]
        html_filename = f"{base_name}.html"
        
        print(f"\nConverting '{docx_filename}' to '{html_filename}'...")
        convert_docx_to_html_slides(docx_filename, html_filename)

if __name__ == "__main__":
    main()
