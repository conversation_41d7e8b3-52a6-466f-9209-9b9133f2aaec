/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主要内容区域 */
main {
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-gap: 30px;
    margin-bottom: 30px;
}

/* 表单样式 */
.input-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: fit-content;
    animation: fadeInUp 0.6s ease;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.input-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.input-section h2 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

button:active {
    transform: translateY(0);
}

/* 图表区域 */
.chart-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 0.6s ease 0.2s both;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chart-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.chart-header h2 {
    color: #667eea;
    margin: 0;
    font-size: 1.5rem;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.standard-switch {
    display: flex;
    align-items: center;
    gap: 8px;
}

.standard-switch label {
    font-weight: 500;
    color: #555;
    font-size: 0.9rem;
}

.standard-switch select {
    padding: 6px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.standard-switch select:focus {
    outline: none;
    border-color: #667eea;
}

.fullscreen-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.fullscreen-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.chart-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #e1e5e9;
}

.tab-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.chart-container {
    height: 600px;
    position: relative;
    background: #fafafa;
    border-radius: 8px;
    padding: 10px;
}

/* 全屏模态框样式 */
.fullscreen-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.fullscreen-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.fullscreen-content {
    background: white;
    border-radius: 12px;
    width: 95%;
    height: 90%;
    max-width: 1400px;
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease;
}

.fullscreen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 2px solid #e1e5e9;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.fullscreen-header h3 {
    margin: 0;
    font-size: 1.4rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.fullscreen-chart-container {
    flex: 1;
    padding: 30px;
    position: relative;
    background: #fafafa;
}

/* 分析报告和历史记录 */
.analysis-section,
.history-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
    animation: fadeInUp 0.6s ease 0.4s both;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.analysis-section:hover,
.history-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.analysis-section h2,
.history-section h2 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.report-content {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.measurement-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.percentile-analysis {
    margin: 20px 0;
}

.percentile-item {
    background: #fff;
    padding: 15px;
    margin: 10px 0;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trend-analysis {
    background: #e8f4fd;
    padding: 15px;
    border-radius: 6px;
    margin: 20px 0;
    border-left: 4px solid #3498db;
}

.recommendations {
    background: #f0f8f0;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #27ae60;
}

.recommendations ul {
    margin: 10px 0;
    padding-left: 20px;
}

.recommendations li {
    margin: 8px 0;
    line-height: 1.5;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.history-actions {
    display: flex;
    gap: 10px;
}

.demo-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    padding: 8px 16px;
    font-size: 14px;
}

.export-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    padding: 8px 16px;
    font-size: 14px;
}

.clear-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    padding: 8px 16px;
    font-size: 14px;
}

.success-message {
    background: #27ae60 !important;
}

.history-list {
    max-height: 300px;
    overflow-y: auto;
}

/* 加载和错误提示 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #e74c3c;
    color: white;
    padding: 15px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.hidden {
    display: none !important;
}

/* 底部样式 */
footer {
    text-align: center;
    padding: 20px;
    color: #666;
    border-top: 1px solid #e1e5e9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    main {
        grid-template-columns: 1fr;
        grid-gap: 20px;
    }
    
    .chart-container {
        height: 400px;
    }

    .chart-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .chart-controls {
        justify-content: space-between;
    }

    .fullscreen-content {
        width: 98%;
        height: 95%;
    }

    .fullscreen-header {
        padding: 15px 20px;
    }

    .fullscreen-chart-container {
        padding: 20px;
    }

    .form-group input,
    .form-group select {
        font-size: 16px; /* 防止iOS缩放 */
    }
}
