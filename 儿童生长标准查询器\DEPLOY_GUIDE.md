# 部署指南 - Edge MCP

## 🚀 快速部署步骤

### 方法一：使用部署脚本（推荐）

1. **准备文件**
   ```bash
   cd 儿童生长标准查询器
   chmod +x deploy.sh
   ./deploy.sh
   ```

2. **上传到Edge MCP**
   - 登录 Edge MCP 控制台
   - 创建新项目
   - 上传生成的 `child-growth-checker.zip`

### 方法二：手动部署

1. **登录Edge MCP控制台**
   - 访问 Edge MCP 官网
   - 登录您的账户

2. **创建新项目**
   - 点击"新建项目"
   - 选择"静态网站"类型
   - 项目名称：儿童生长标准查询器

3. **上传文件**
   - 将以下文件上传到项目根目录：
     - `index.html`
     - `manifest.json`
     - `sw.js`
     - `deploy.json`

4. **配置项目**
   - 入口文件：`index.html`
   - 构建命令：留空
   - 输出目录：`.`

5. **域名配置**
   - 使用默认域名或绑定自定义域名
   - 启用 HTTPS（推荐）

6. **性能优化**
   - 启用 CDN 加速
   - 启用 Gzip 压缩
   - 设置缓存策略：静态资源缓存 1 天

## 🔧 高级配置

### 环境变量
无需配置环境变量，应用为纯前端项目。

### 自定义域名
1. 在域名管理中添加 CNAME 记录
2. 在 Edge MCP 中绑定域名
3. 申请 SSL 证书

### CDN 配置
```json
{
  "cache": {
    "html": "public, max-age=3600",
    "static": "public, max-age=86400",
    "api": "no-cache"
  }
}
```

## 📱 PWA 部署验证

部署完成后，请验证以下功能：

1. **访问测试**
   - 在手机浏览器中访问部署的URL
   - 检查页面是否正常显示

2. **PWA 功能**
   - 查看是否出现"安装应用"提示
   - 测试添加到主屏幕功能
   - 验证离线访问能力

3. **功能测试**
   - 输入测试数据
   - 验证查询结果准确性
   - 测试响应式布局

## 🌐 访问地址

部署成功后，您的应用将可通过以下地址访问：

- **默认域名**: `https://your-project.edge-mcp.com`
- **自定义域名**: `https://your-domain.com`

## 📊 监控和维护

### 访问统计
- 在 Edge MCP 控制台查看访问统计
- 监控用户使用情况

### 更新部署
1. 修改本地文件
2. 重新打包上传
3. 清除 CDN 缓存

### 备份
- 定期备份项目文件
- 导出访问日志

## 🆘 常见问题

### Q: 页面无法访问
A: 检查域名解析和SSL证书配置

### Q: PWA 无法安装
A: 确保 manifest.json 和 sw.js 文件正确部署

### Q: 数据不准确
A: 检查生长标准数据是否正确加载

### Q: 移动端显示异常
A: 检查 viewport 设置和 CSS 媒体查询

## 📞 技术支持

如遇到部署问题，请：
1. 检查控制台错误信息
2. 查看 Edge MCP 部署日志
3. 联系技术支持团队

---

🎉 **恭喜！您的儿童生长标准查询器已成功部署！**
