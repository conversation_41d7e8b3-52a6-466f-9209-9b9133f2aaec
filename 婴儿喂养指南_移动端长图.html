<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>婴儿喂养与体重增长完整指南</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #FFF8ED 0%, #FFFCF5 100%);
            color: #333;
            width: 320px;
            margin: 0 auto;
            padding: 0;
            line-height: 1.6;
        }
        .container {
            width: 100%;
            padding: 16px;
            box-sizing: border-box;
        }
        .section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #f1f5f9;
        }
        .title {
            font-size: 24px;
            font-weight: 700;
            color: #0d9488;
            text-align: center;
            margin-bottom: 16px;
            line-height: 1.3;
        }
        .subtitle {
            font-size: 18px;
            font-weight: 600;
            color: #0f766e;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .text {
            font-size: 14px;
            color: #475569;
            margin-bottom: 12px;
            line-height: 1.6;
        }
        .highlight-box {
            background: #f0fdfa;
            border: 2px solid #5eead4;
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
        }
        .chart-container {
            width: 100%;
            height: 250px;
            margin: 16px 0;
        }
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            margin: 16px 0;
        }
        .card {
            background: #fef7ed;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            border: 2px solid #fed7aa;
        }
        .emoji {
            font-size: 32px;
            margin-bottom: 8px;
            display: block;
        }
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #ea580c;
            margin-bottom: 8px;
        }
        .food-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin: 16px 0;
        }
        .food-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }
        .food-emoji {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
        }
        .food-name {
            font-size: 14px;
            font-weight: 600;
            color: #334155;
        }
        .qa-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            border-left: 4px solid #0d9488;
        }
        .qa-question {
            font-size: 16px;
            font-weight: 600;
            color: #0f766e;
            margin-bottom: 8px;
        }
        .qa-answer {
            font-size: 14px;
            color: #475569;
            line-height: 1.6;
        }
        .formula-table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
            font-size: 13px;
        }
        .formula-table th,
        .formula-table td {
            border: 1px solid #e2e8f0;
            padding: 8px;
            text-align: center;
        }
        .formula-table th {
            background: #0d9488;
            color: white;
            font-weight: 600;
        }
        .header {
            background: linear-gradient(135deg, #0d9488 0%, #14b8a6 100%);
            color: white;
            text-align: center;
            padding: 32px 20px;
            margin-bottom: 0;
        }
        .header-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1.2;
        }
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.4;
        }
        .convert-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #0d9488;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(13, 148, 136, 0.3);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .convert-btn:hover {
            background: #0f766e;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(13, 148, 136, 0.4);
        }
        .convert-btn:active {
            transform: translateY(0);
        }
        #content-area {
            position: relative;
        }
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Convert Button -->
    <button id="convertBtn" class="convert-btn" onclick="convertToImage()">📸 转为图片</button>

    <!-- Content Area -->
    <div id="content-area">
        <!-- Header -->
        <div class="header">
            <h1 class="header-title">婴儿喂养与体重增长</h1>
            <p class="header-subtitle">科学指南 · 专业建议 · 实用工具</p>
        </div>

        <div class="container">
        <!-- Section 1: Growth Assessment -->
        <div class="section">
            <h2 class="title">📊 理解宝宝的生长曲线</h2>
            <p class="text">生长曲线是评估宝宝健康的关键工具，但不同的标准讲述着不同的故事。了解WHO和CDC标准的区别，是科学解读宝宝成长的第一步。</p>
            
            <div class="chart-container">
                <canvas id="growthChart"></canvas>
            </div>
            
            <div class="highlight-box">
                <h3 class="subtitle">🏆 WHO生长标准 (黄金标准)</h3>
                <p class="text">这是一个"规范性"标准，描述了在理想条件下（以母乳喂养为主）宝宝应该如何生长。它将母乳喂养的独特生长模式（初期快，后放缓）正常化。</p>
            </div>
            
            <div style="background: #f1f5f9; border: 2px solid #cbd5e1; border-radius: 12px; padding: 16px;">
                <h3 class="subtitle">📈 CDC生长图表 (历史参考)</h3>
                <p class="text">这是一个"描述性"参考，反映了过去美国婴儿（以配方奶喂养为主）的实际生长情况。其体重增长模式更线性、更快速。</p>
            </div>
        </div>

        <!-- Section 2: Breastfeeding -->
        <div class="section">
            <h2 class="title">🤱 母乳喂养：黄金标准</h2>
            <p class="text">母乳是宝宝最理想的食物。由于无法直接测量奶量，我们可以通过观察一系列可靠的迹象来判断宝宝是否获得了充足的营养。</p>
            
            <h3 class="subtitle">✅ 营养充足性自查清单</h3>
            <div class="grid-3">
                <div class="card">
                    <span class="emoji">🍼</span>
                    <h4 class="card-title">喂养行为</h4>
                    <p class="text">24小时内喂养8-12次。喂奶时能听到吞咽声。喂完后宝宝看起来满足、放松。</p>
                </div>
                <div class="card">
                    <span class="emoji">⚖️</span>
                    <h4 class="card-title">体重增长</h4>
                    <p class="text">出生10-14天内恢复出生体重。之后体重沿自己的生长曲线稳定增长。</p>
                </div>
                <div class="card">
                    <span class="emoji">🚼</span>
                    <h4 class="card-title">尿布输出</h4>
                    <p class="text">出生第5天后，每天至少6片湿尿布（尿液清澈或淡黄），和3-4次黄色糊状大便。</p>
                </div>
            </div>
        </div>

        <!-- Section 3: Formula Feeding -->
        <div class="section">
            <h2 class="title">🍼 配方奶：安全健康的选择</h2>
            <p class="text">对于配方奶喂养的宝宝，了解大致的喂养量和响应式喂养技巧，有助于支持宝宝健康成长，同时避免过度喂养。</p>
            
            <h3 class="subtitle">📋 配方奶喂养量参考表</h3>
            <table class="formula-table">
                <thead>
                    <tr>
                        <th>月龄</th>
                        <th>每日总奶量</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>新生儿</td><td>240-720 mL</td></tr>
                    <tr><td>1个月</td><td>540-960 mL</td></tr>
                    <tr><td>2-3个月</td><td>600-1170 mL</td></tr>
                    <tr><td>4-5个月</td><td>600-1080 mL</td></tr>
                    <tr><td>6-8个月</td><td>720-960 mL</td></tr>
                    <tr><td>9-11个月</td><td>840-960+ mL</td></tr>
                    <tr><td>12个月</td><td>630-960 mL</td></tr>
                </tbody>
            </table>
            
            <div class="highlight-box">
                <h4 class="subtitle">💡 预防过度喂养小贴士</h4>
                <p class="text">瓶喂时，采用"慢速瓶喂法"：让宝宝近乎直立，奶瓶保持水平，让宝宝主导吸吮节奏，喂几分钟后可暂停拍嗝，给宝宝感知饱腹感的机会。</p>
            </div>
        </div>

        <!-- Section 4: Solid Foods -->
        <div class="section">
            <h2 class="title">🥄 辅食添加：开启味觉新旅程</h2>
            <p class="text">约6个月大，当宝宝准备好时，就可以引入辅食了。选择营养密集的食物，并以响应式、安全的方式喂养是关键。</p>

            <h3 class="subtitle">🎯 宝宝准备好了吗？</h3>
            <div class="grid-3">
                <div style="background: #fef3c7; border: 2px solid #fbbf24; border-radius: 12px; padding: 12px; text-align: center;">
                    <span style="font-size: 20px;">✓</span>
                    <p style="font-size: 13px; margin: 4px 0 0 0; color: #92400e;">能在支撑下坐直</p>
                </div>
                <div style="background: #fef3c7; border: 2px solid #fbbf24; border-radius: 12px; padding: 12px; text-align: center;">
                    <span style="font-size: 20px;">✓</span>
                    <p style="font-size: 13px; margin: 4px 0 0 0; color: #92400e;">头部控制良好</p>
                </div>
                <div style="background: #fef3c7; border: 2px solid #fbbf24; border-radius: 12px; padding: 12px; text-align: center;">
                    <span style="font-size: 20px;">✓</span>
                    <p style="font-size: 13px; margin: 4px 0 0 0; color: #92400e;">挺舌反射消失</p>
                </div>
            </div>

            <h3 class="subtitle">🍎 首批辅食的营养选择</h3>
            <div class="food-grid">
                <div class="food-card">
                    <span class="food-emoji">🍗</span>
                    <h4 class="food-name">鸡肉泥</h4>
                    <p style="font-size: 12px; color: #64748b; margin-top: 4px;">高吸收率铁、优质蛋白</p>
                </div>
                <div class="food-card">
                    <span class="food-emoji">🥑</span>
                    <h4 class="food-name">牛油果</h4>
                    <p style="font-size: 12px; color: #64748b; margin-top: 4px;">健康脂肪、促进大脑发育</p>
                </div>
                <div class="food-card">
                    <span class="food-emoji">🍠</span>
                    <h4 class="food-name">红薯</h4>
                    <p style="font-size: 12px; color: #64748b; margin-top: 4px;">维生素A、维生素C</p>
                </div>
                <div class="food-card">
                    <span class="food-emoji">🌾</span>
                    <h4 class="food-name">强化铁米粉</h4>
                    <p style="font-size: 12px; color: #64748b; margin-top: 4px;">强化铁、易于消化</p>
                </div>
                <div class="food-card">
                    <span class="food-emoji">🍌</span>
                    <h4 class="food-name">香蕉</h4>
                    <p style="font-size: 12px; color: #64748b; margin-top: 4px;">钾、维生素B6、易于接受</p>
                </div>
            </div>

            <div class="highlight-box">
                <h4 class="subtitle">🍗 鸡肉泥详情</h4>
                <p class="text">优质蛋白质和高生物利用率的血红素铁的极佳来源，支持肌肉和血液健康。</p>
            </div>

            <div style="background: #f0f9ff; border: 2px solid #7dd3fc; border-radius: 12px; padding: 16px; margin: 12px 0;">
                <h4 class="subtitle">🥑 牛油果详情</h4>
                <p class="text">富含健康的单不饱和脂肪，对宝宝的大脑和神经系统发育至关重要。</p>
            </div>

            <div style="background: #fef7ed; border: 2px solid #fdba74; border-radius: 12px; padding: 16px; margin: 12px 0;">
                <h4 class="subtitle">🍠 红薯详情</h4>
                <p class="text">β-胡萝卜素（维生素A）的极佳来源，支持视力和免疫系统，同时富含维生素C。</p>
            </div>
        </div>

        <!-- Section 5: Common Issues -->
        <div class="section">
            <h2 class="title">❓ 常见喂养问题解答</h2>
            <p class="text">在喂养过程中遇到困惑是正常的。这里我们解答一些常见问题，帮助您从容应对。</p>

            <div class="qa-item">
                <h3 class="qa-question">Q1: 宝宝体重增长慢怎么办？</h3>
                <p class="qa-answer">首先，请咨询儿科医生进行专业评估。常见原因包括：无效的乳汁转移（衔乳姿势不当）、喂养频率不足（未按需喂养，每天应达8-12次）、或母亲乳汁供应问题。管理策略包括：改善喂养姿势、增加喂养频率。在医生指导下，有时可能需要补充喂养。</p>
            </div>

            <div class="qa-item">
                <h3 class="qa-question">Q2: 如何判断是否过度喂养？</h3>
                <p class="qa-answer">过度喂养在瓶喂宝宝中更常见。迹象包括：喂食后烦躁不安、胀气、频繁大量的吐奶（非普通溢奶）、体重增长过快。预防的关键是"响应式喂养"：学习并尊重宝宝的饱腹信号（如转头、闭嘴）。采用"慢速瓶喂法"能有效帮助宝宝控制进食节奏。</p>
            </div>

            <div class="qa-item">
                <h3 class="qa-question">Q3: 什么时候可以引入牛奶？(根据2023 WHO新指南)</h3>
                <p class="qa-answer">对于6-11个月的非母乳喂养宝宝，在保证辅食营养全面、富含铁质的前提下，可以将全脂牛奶作为配方奶的替代选项之一。关键在于，牛奶不能取代营养丰富的辅食，尤其是富含铁的食物。对于1岁以上幼儿，WHO推荐直接饮用动物奶，而非"成长配方奶"。</p>
            </div>

            <div class="qa-item">
                <h3 class="qa-question">Q4: 如何引入花生、鸡蛋等易过敏食物？</h3>
                <p class="qa-answer">与旧观念相反，新研究表明在宝宝约6个月大时尽早引入常见致敏食物，可能有助于降低过敏风险。原则是：一次只引入一种新食物，少量开始，连续观察3-5天，看有无皮疹、呕吐、腹泻等不良反应。如果没有，可逐渐增加并纳入日常饮食。对于有严重湿疹或已知食物过敏史的宝宝，请在医生指导下进行。</p>
            </div>
        </div>

        <!-- Footer -->
        <div style="background: #f8fafc; border-radius: 16px; padding: 24px; text-align: center; border: 1px solid #e2e8f0;">
            <p style="font-size: 13px; color: #64748b; line-height: 1.6; margin: 0;">
                本应用内容基于《婴儿体重与母乳、配方奶及辅食喂养实践的综合分析》报告制作，仅供参考，不能替代专业的医疗建议。<br><br>
                如有任何健康问题，请咨询您的儿科医生。
            </p>
        </div>
    </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Growth Chart
        const ctx = document.getElementById('growthChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['出生', '1月', '2月', '3月', '4月', '6月', '9月', '12月'],
                datasets: [
                    {
                        label: '母乳喂养 (WHO 标准)',
                        data: [3.3, 4.4, 5.6, 6.4, 7.0, 7.9, 8.9, 9.6],
                        borderColor: '#0d9488',
                        backgroundColor: 'rgba(13, 148, 136, 0.1)',
                        borderWidth: 3,
                        tension: 0.3,
                        fill: true,
                    },
                    {
                        label: '配方奶喂养 (CDC 参考)',
                        data: [3.3, 4.5, 5.8, 6.8, 7.6, 8.8, 10.0, 10.8],
                        borderColor: '#9ca3af',
                        backgroundColor: 'rgba(156, 163, 175, 0.1)',
                        borderWidth: 3,
                        tension: 0.3,
                        borderDash: [5, 5],
                        fill: true,
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '婴幼儿体重增长模式对比',
                        font: { size: 14, family: "'Noto Sans SC', sans-serif" },
                        color: '#333'
                    },
                    legend: {
                        labels: {
                            font: { size: 11 }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: '体重 (kg)',
                            font: { size: 11 }
                        },
                        ticks: {
                            font: { size: 10 }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '月龄',
                            font: { size: 11 }
                        },
                        ticks: {
                            font: { size: 10 }
                        }
                    }
                }
            }
        });
    });

    // Convert to Image Function
    async function convertToImage() {
        const btn = document.getElementById('convertBtn');
        const contentArea = document.getElementById('content-area');

        // Show loading state
        btn.textContent = '🔄 生成中...';
        btn.disabled = true;
        contentArea.classList.add('loading');

        try {
            // Hide the button temporarily
            btn.style.display = 'none';

            // Generate image
            const canvas = await html2canvas(contentArea, {
                width: 320,
                height: contentArea.scrollHeight,
                scale: 2, // Higher quality
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#FFF8ED',
                scrollX: 0,
                scrollY: 0
            });

            // Create download link
            const link = document.createElement('a');
            link.download = '婴儿喂养指南_完整长图.png';
            link.href = canvas.toDataURL('image/png');

            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show success message
            btn.textContent = '✅ 已保存';
            setTimeout(() => {
                btn.textContent = '📸 转为图片';
                btn.disabled = false;
            }, 2000);

        } catch (error) {
            console.error('转换失败:', error);
            btn.textContent = '❌ 转换失败';
            setTimeout(() => {
                btn.textContent = '📸 转为图片';
                btn.disabled = false;
            }, 2000);
        } finally {
            // Show button again
            btn.style.display = 'block';
            contentArea.classList.remove('loading');
        }
    }
    </script>
</body>
</html>
