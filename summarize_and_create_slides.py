import os
import glob
import traceback
from docx import Document
from transformers import pipeline, AutoTokenizer, AutoModelForSeq2SeqLM

# --- CONFIGURATION ---
# Using a smaller, multilingual model for efficiency
MODEL_NAME = "google/flan-t5-small"
# The summarizer will try to keep the summary length between MIN_LENGTH and MAX_LENGTH tokens
MIN_LENGTH = 20
MAX_LENGTH = 100 

# --- INITIALIZE NLP MODEL ---
# This will download the model on the first run
print("Initializing NLP model... (This might take a while on the first run)")
try:
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
    model = AutoModelForSeq2SeqLM.from_pretrained(MODEL_NAME)
    summarizer = pipeline("summarization", model=model, tokenizer=tokenizer)
    print("NLP model initialized successfully.")
except Exception as e:
    print(f"Fatal Error: Could not initialize NLP model. Please check your internet connection.")
    print(f"Error details: {e}")
    summarizer = None # Ensure summarizer is None if it fails

def is_heading(paragraph):
    """
    Heuristic to determine if a paragraph is a heading.
    Checks for style names or simple formatting like being short and bold.
    """
    style_name = paragraph.style.name
    if 'Heading' in style_name or '标题' in style_name:
        return True
    if len(paragraph.text.strip()) < 50 and all(run.bold for run in paragraph.runs if run.text.strip()):
        return True
    return False

def summarize_text(text):
    """
    Summarizes a given block of text using the pre-trained model.
    """
    if not summarizer:
        print("Summarizer not available. Returning original text.")
        return text # Fallback if model fails to load

    # Prepending "summarize: " is a common practice for T5-based models
    summary = summarizer(
        "summarize: " + text,
        min_length=MIN_LENGTH,
        max_length=MAX_LENGTH,
        truncation=True
    )
    return summary[0]['summary_text']

def generate_revealjs_html(title, slides):
    """
    Generates the final HTML for the Reveal.js presentation.
    """
    slides_html = f"<section><h1>{title}</h1></section>\n"
    for slide in slides:
        slide_content_html = f"<h2>{slide['title']}</h2>\n"
        if slide['summary']:
            # Present the summary as a blockquote for emphasis
            slide_content_html += f"<blockquote>{slide['summary']}</blockquote>\n"
        
        slides_html += f"<section>\n{slide_content_html}</section>\n"

    return f"""
<!doctype html>
<html lang=\"en\">
    <head>
        <meta charset=\"utf-8\">
        <title>{title}</title>
        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">
        <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reset.min.css\">
        <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.css\">
        <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/simple.min.css\" id=\"theme\">
    </head>
    <body>
        <div class=\"reveal\">
            <div class=\"slides\">
                {slides_html}
            </div>
        </div>
        <script src=\"https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.js\"></script>
        <script>
            Reveal.initialize({{ hash: true }});
        </script>
    </body>
</html>
"""

def convert_and_summarize(docx_path, html_path):
    """
    The core function to read, summarize, and convert the DOCX to HTML slides.
    """
    try:
        document = Document(docx_path)
        slides = []
        current_content = ""
        current_title = ""
        main_title = "Presentation"

        if document.paragraphs:
            main_title = document.paragraphs[0].text
            current_title = main_title

        for para in document.paragraphs[1:]:
            if not para.text.strip():
                continue

            if is_heading(para):
                # When we find a new heading, summarize the content of the previous section
                if current_title and current_content:
                    print(f"Summarizing content for slide: '{current_title}'...")
                    summary = summarize_text(current_content)
                    slides.append({'title': current_title, 'summary': summary})
                
                # Start a new slide
                current_title = para.text
                current_content = ""
            else:
                current_content += para.text + "\n"
        
        # Process the last slide
        if current_title and current_content:
            print(f"Summarizing content for the last slide: '{current_title}'...")
            summary = summarize_text(current_content)
            slides.append({'title': current_title, 'summary': summary})

        html_content = generate_revealjs_html(main_title, slides)
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        print(f"Successfully created summarized HTML slides: {os.path.basename(html_path)}")

    except Exception as e:
        print(f"An error occurred during conversion of {os.path.basename(docx_path)}: {e}")
        print(traceback.format_exc())

def main():
    if not summarizer:
        print("Cannot proceed without a functional NLP model. Exiting.")
        return

    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    docx_files = glob.glob("*.docx")
    if not docx_files:
        print("No .docx files found.")
        return

    print(f"Found .docx files: {docx_files}")
    for docx_filename in docx_files:
        base_name = os.path.splitext(docx_filename)[0]
        html_filename = f"{base_name}_summarized.html" # New filename to avoid overwriting
        
        print(f"\n--- Processing '{docx_filename}' ---")
        convert_and_summarize(docx_filename, html_filename)

if __name__ == "__main__":
    main()